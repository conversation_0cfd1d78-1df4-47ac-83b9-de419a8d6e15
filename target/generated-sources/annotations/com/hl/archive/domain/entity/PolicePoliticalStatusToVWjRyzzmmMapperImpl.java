package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjRyzzmm;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-26T09:36:53+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PolicePoliticalStatusToVWjRyzzmmMapperImpl implements PolicePoliticalStatusToVWjRyzzmmMapper {

    @Override
    public VWjRyzzmm convert(PolicePoliticalStatus source) {
        if ( source == null ) {
            return null;
        }

        VWjRyzzmm vWjRyzzmm = new VWjRyzzmm();

        vWjRyzzmm.setGmsfhm( source.getIdCard() );
        vWjRyzzmm.setZzsf( source.getPoliticalIdentity() );
        if ( source.getJoinPartyDate() != null ) {
            vWjRyzzmm.setCjdpsj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getJoinPartyDate() ) );
        }

        return vWjRyzzmm;
    }

    @Override
    public VWjRyzzmm convert(PolicePoliticalStatus source, VWjRyzzmm target) {
        if ( source == null ) {
            return target;
        }

        target.setGmsfhm( source.getIdCard() );
        target.setZzsf( source.getPoliticalIdentity() );
        if ( source.getJoinPartyDate() != null ) {
            target.setCjdpsj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getJoinPartyDate() ) );
        }
        else {
            target.setCjdpsj( null );
        }

        return target;
    }
}
