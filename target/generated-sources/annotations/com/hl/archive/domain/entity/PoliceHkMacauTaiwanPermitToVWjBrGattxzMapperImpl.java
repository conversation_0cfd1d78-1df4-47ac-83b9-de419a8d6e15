package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjBrGattxz;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-26T09:36:53+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceHkMacauTaiwanPermitToVWjBrGattxzMapperImpl implements PoliceHkMacauTaiwanPermitToVWjBrGattxzMapper {

    @Override
    public VWjBrGattxz convert(PoliceHkMacauTaiwanPermit source) {
        if ( source == null ) {
            return null;
        }

        VWjBrGattxz vWjBrGattxz = new VWjBrGattxz();

        vWjBrGattxz.setBgjgmc( source.getCustodyOrganization() );
        if ( source.getIssueDate() != null ) {
            vWjBrGattxz.setQfrq( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getIssueDate() ) );
        }
        if ( source.getExpiryDate() != null ) {
            vWjBrGattxz.setYxqz( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getExpiryDate() ) );
        }
        vWjBrGattxz.setGmsfhm( source.getIdCard() );
        vWjBrGattxz.setBz( source.getRemarks() );
        vWjBrGattxz.setZjhm( source.getDocumentNumber() );
        vWjBrGattxz.setZjmc( source.getDocumentName() );

        return vWjBrGattxz;
    }

    @Override
    public VWjBrGattxz convert(PoliceHkMacauTaiwanPermit source, VWjBrGattxz target) {
        if ( source == null ) {
            return target;
        }

        target.setBgjgmc( source.getCustodyOrganization() );
        if ( source.getIssueDate() != null ) {
            target.setQfrq( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getIssueDate() ) );
        }
        else {
            target.setQfrq( null );
        }
        if ( source.getExpiryDate() != null ) {
            target.setYxqz( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getExpiryDate() ) );
        }
        else {
            target.setYxqz( null );
        }
        target.setGmsfhm( source.getIdCard() );
        target.setBz( source.getRemarks() );
        target.setZjhm( source.getDocumentNumber() );
        target.setZjmc( source.getDocumentName() );

        return target;
    }
}
