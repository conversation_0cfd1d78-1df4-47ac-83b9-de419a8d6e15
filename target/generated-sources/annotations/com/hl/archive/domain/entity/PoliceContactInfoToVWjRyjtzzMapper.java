package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjRyjtzz;
import com.hl.orasync.domain.VWjRyjtzzToPoliceContactInfoMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__606;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__606.class,
    uses = {ConversionUtils.class,VWjRyjtzzToPoliceContactInfoMapper.class},
    imports = {}
)
public interface PoliceContactInfoToVWjRyjtzzMapper extends BaseMapper<PoliceContactInfo, VWjRyjtzz> {
  @Mapping(
      target = "jtdz",
      source = "homeAddress"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "shhmdh",
      source = "mobileShortNumber"
  )
  @Mapping(
      target = "jtdh",
      source = "homePhone"
  )
  @Mapping(
      target = "shhm",
      source = "mobilePhone"
  )
  VWjRyjtzz convert(PoliceContactInfo source);

  @Mapping(
      target = "jtdz",
      source = "homeAddress"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "shhmdh",
      source = "mobileShortNumber"
  )
  @Mapping(
      target = "jtdh",
      source = "homePhone"
  )
  @Mapping(
      target = "shhm",
      source = "mobilePhone"
  )
  VWjRyjtzz convert(PoliceContactInfo source, @MappingTarget VWjRyjtzz target);
}
