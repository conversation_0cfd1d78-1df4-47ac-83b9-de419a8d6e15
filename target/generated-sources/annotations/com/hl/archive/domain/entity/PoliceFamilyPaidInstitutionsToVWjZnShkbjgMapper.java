package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjZnShkbjg;
import com.hl.orasync.domain.VWjZnShkbjgToPoliceFamilyPaidInstitutionsMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__607;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__607.class,
    uses = {ConversionUtils.class,VWjZnShkbjgToPoliceFamilyPaidInstitutionsMapper.class},
    imports = {}
)
public interface PoliceFamilyPaidInstitutionsToVWjZnShkbjgMapper extends BaseMapper<PoliceFamilyPaidInstitutions, VWjZnShkbjg> {
  @Mapping(
      target = "jyfw",
      source = "businessScope"
  )
  @Mapping(
      target = "jyd",
      source = "businessAddress"
  )
  @Mapping(
      target = "qylxmc",
      source = "institutionType"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "xmFr",
      source = "name"
  )
  @Mapping(
      target = "zczb",
      source = "registeredCapital"
  )
  @Mapping(
      target = "zcd",
      source = "registrationAddress"
  )
  @Mapping(
      target = "qymc",
      source = "institutionName"
  )
  @Mapping(
      target = "zch",
      source = "socialCreditCode"
  )
  @Mapping(
      target = "clsj",
      source = "establishmentDate"
  )
  VWjZnShkbjg convert(PoliceFamilyPaidInstitutions source);

  @Mapping(
      target = "jyfw",
      source = "businessScope"
  )
  @Mapping(
      target = "jyd",
      source = "businessAddress"
  )
  @Mapping(
      target = "qylxmc",
      source = "institutionType"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "xmFr",
      source = "name"
  )
  @Mapping(
      target = "zczb",
      source = "registeredCapital"
  )
  @Mapping(
      target = "zcd",
      source = "registrationAddress"
  )
  @Mapping(
      target = "qymc",
      source = "institutionName"
  )
  @Mapping(
      target = "zch",
      source = "socialCreditCode"
  )
  @Mapping(
      target = "clsj",
      source = "establishmentDate"
  )
  VWjZnShkbjg convert(PoliceFamilyPaidInstitutions source, @MappingTarget VWjZnShkbjg target);
}
