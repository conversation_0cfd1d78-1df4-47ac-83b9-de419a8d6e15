package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjMjqyxxsb;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-26T13:43:23+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceInjuryDeclareToVWjMjqyxxsbMapperImpl implements PoliceInjuryDeclareToVWjMjqyxxsbMapper {

    @Override
    public VWjMjqyxxsb convert(PoliceInjuryDeclare source) {
        if ( source == null ) {
            return null;
        }

        VWjMjqyxxsb vWjMjqyxxsb = new VWjMjqyxxsb();

        vWjMjqyxxsb.setSqlbmc( source.getDeclareType() );
        vWjMjqyxxsb.setXm( source.getName() );
        vWjMjqyxxsb.setGmsfhm( source.getIdCard() );
        vWjMjqyxxsb.setGzdwGajgmc( source.getOrgName() );
        vWjMjqyxxsb.setZwmc( source.getPosition() );
        vWjMjqyxxsb.setHcrdztmc( source.getCurrentStatus() );
        vWjMjqyxxsb.setSqly( source.getInjuryEvent() );
        vWjMjqyxxsb.setJh( source.getPoliceNumber() );

        return vWjMjqyxxsb;
    }

    @Override
    public VWjMjqyxxsb convert(PoliceInjuryDeclare source, VWjMjqyxxsb target) {
        if ( source == null ) {
            return target;
        }

        target.setSqlbmc( source.getDeclareType() );
        target.setXm( source.getName() );
        target.setGmsfhm( source.getIdCard() );
        target.setGzdwGajgmc( source.getOrgName() );
        target.setZwmc( source.getPosition() );
        target.setHcrdztmc( source.getCurrentStatus() );
        target.setSqly( source.getInjuryEvent() );
        target.setJh( source.getPoliceNumber() );

        return target;
    }
}
