package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjQtQtsx;
import com.hl.orasync.domain.VWjQtQtsxToPoliceOtherMattersMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__606;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__606.class,
    uses = {ConversionUtils.class,VWjQtQtsxToPoliceOtherMattersMapper.class},
    imports = {}
)
public interface PoliceOtherMattersToVWjQtQtsxMapper extends BaseMapper<PoliceOtherMatters, VWjQtQtsx> {
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "bz",
      source = "reportContent"
  )
  @Mapping(
      target = "djrq",
      source = "registrationDate"
  )
  VWjQtQtsx convert(PoliceOtherMatters source);

  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "bz",
      source = "reportContent"
  )
  @Mapping(
      target = "djrq",
      source = "registrationDate"
  )
  VWjQtQtsx convert(PoliceOtherMatters source, @MappingTarget VWjQtQtsx target);
}
