package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjMjqyxxsb;
import com.hl.orasync.domain.VWjMjqyxxsbToPoliceInjuryDeclareMapper;
import io.github.linpeilie.AutoMapperConfig__606;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__606.class,
    uses = {VWjMjqyxxsbToPoliceInjuryDeclareMapper.class},
    imports = {}
)
public interface PoliceInjuryDeclareToVWjMjqyxxsbMapper extends BaseMapper<PoliceInjuryDeclare, VWjMjqyxxsb> {
  @Mapping(
      target = "sqlbmc",
      source = "declareType"
  )
  @Mapping(
      target = "xm",
      source = "name"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "gzdwGajgmc",
      source = "orgName"
  )
  @Mapping(
      target = "zwmc",
      source = "position"
  )
  @Mapping(
      target = "hcrdztmc",
      source = "currentStatus"
  )
  @Mapping(
      target = "sqly",
      source = "injuryEvent"
  )
  @Mapping(
      target = "jh",
      source = "policeNumber"
  )
  VWjMjqyxxsb convert(PoliceInjuryDeclare source);

  @Mapping(
      target = "sqlbmc",
      source = "declareType"
  )
  @Mapping(
      target = "xm",
      source = "name"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "gzdwGajgmc",
      source = "orgName"
  )
  @Mapping(
      target = "zwmc",
      source = "position"
  )
  @Mapping(
      target = "hcrdztmc",
      source = "currentStatus"
  )
  @Mapping(
      target = "sqly",
      source = "injuryEvent"
  )
  @Mapping(
      target = "jh",
      source = "policeNumber"
  )
  VWjMjqyxxsb convert(PoliceInjuryDeclare source, @MappingTarget VWjMjqyxxsb target);
}
