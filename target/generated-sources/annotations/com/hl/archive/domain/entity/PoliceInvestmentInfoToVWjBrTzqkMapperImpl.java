package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjBrTzqk;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-26T13:29:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceInvestmentInfoToVWjBrTzqkMapperImpl implements PoliceInvestmentInfoToVWjBrTzqkMapper {

    @Override
    public VWjBrTzqk convert(PoliceInvestmentInfo source) {
        if ( source == null ) {
            return null;
        }

        VWjBrTzqk vWjBrTzqk = new VWjBrTzqk();

        vWjBrTzqk.setTzcpmc( source.getInvestmentEntity() );
        vWjBrTzqk.setGmsfhm( source.getIdCard() );
        if ( source.getTransactionDate() != null ) {
            vWjBrTzqk.setTzsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getTransactionDate() ) );
        }
        vWjBrTzqk.setTzqx( source.getInvestmentSource() );
        if ( source.getInvestmentAmount() != null ) {
            vWjBrTzqk.setJe( source.getInvestmentAmount().toString() );
        }

        return vWjBrTzqk;
    }

    @Override
    public VWjBrTzqk convert(PoliceInvestmentInfo source, VWjBrTzqk target) {
        if ( source == null ) {
            return target;
        }

        target.setTzcpmc( source.getInvestmentEntity() );
        target.setGmsfhm( source.getIdCard() );
        if ( source.getTransactionDate() != null ) {
            target.setTzsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getTransactionDate() ) );
        }
        else {
            target.setTzsj( null );
        }
        target.setTzqx( source.getInvestmentSource() );
        if ( source.getInvestmentAmount() != null ) {
            target.setJe( source.getInvestmentAmount().toString() );
        }
        else {
            target.setJe( null );
        }

        return target;
    }
}
