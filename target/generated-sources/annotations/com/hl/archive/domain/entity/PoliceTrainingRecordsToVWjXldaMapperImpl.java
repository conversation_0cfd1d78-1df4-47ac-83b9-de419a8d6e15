package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjXlda;
import java.time.LocalDateTime;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-26T13:29:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceTrainingRecordsToVWjXldaMapperImpl implements PoliceTrainingRecordsToVWjXldaMapper {

    @Override
    public VWjXlda convert(PoliceTrainingRecords source) {
        if ( source == null ) {
            return null;
        }

        VWjXlda vWjXlda = new VWjXlda();

        vWjXlda.setPxbmc( source.getTrainingName() );
        vWjXlda.setGmsfhm( source.getIdCard() );
        vWjXlda.setCj( source.getGrade() );
        vWjXlda.setKpxmmc( source.getExamProjectName() );
        if ( source.getTrainingTime() != null ) {
            vWjXlda.setPxsj( LocalDateTime.parse( source.getTrainingTime() ) );
        }
        vWjXlda.setPfdf( source.getScore() );

        return vWjXlda;
    }

    @Override
    public VWjXlda convert(PoliceTrainingRecords source, VWjXlda target) {
        if ( source == null ) {
            return target;
        }

        target.setPxbmc( source.getTrainingName() );
        target.setGmsfhm( source.getIdCard() );
        target.setCj( source.getGrade() );
        target.setKpxmmc( source.getExamProjectName() );
        if ( source.getTrainingTime() != null ) {
            target.setPxsj( LocalDateTime.parse( source.getTrainingTime() ) );
        }
        else {
            target.setPxsj( null );
        }
        target.setPfdf( source.getScore() );

        return target;
    }
}
