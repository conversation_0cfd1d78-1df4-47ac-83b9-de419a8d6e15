package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjBrWjdc;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-26T13:43:23+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceOrganizationalInquiryToVWjBrWjdcMapperImpl implements PoliceOrganizationalInquiryToVWjBrWjdcMapper {

    @Override
    public VWjBrWjdc convert(PoliceOrganizationalInquiry source) {
        if ( source == null ) {
            return null;
        }

        VWjBrWjdc vWjBrWjdc = new VWjBrWjdc();

        if ( source.getHandlingDate() != null ) {
            vWjBrWjdc.setDcrq( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getHandlingDate() ) );
        }
        vWjBrWjdc.setDcjg( source.getHandlingAuthority() );
        vWjBrWjdc.setGmsfhm( source.getIdCard() );
        vWjBrWjdc.setWfqk( source.getSuspectedViolations() );

        return vWjBrWjdc;
    }

    @Override
    public VWjBrWjdc convert(PoliceOrganizationalInquiry source, VWjBrWjdc target) {
        if ( source == null ) {
            return target;
        }

        if ( source.getHandlingDate() != null ) {
            target.setDcrq( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getHandlingDate() ) );
        }
        else {
            target.setDcrq( null );
        }
        target.setDcjg( source.getHandlingAuthority() );
        target.setGmsfhm( source.getIdCard() );
        target.setWfqk( source.getSuspectedViolations() );

        return target;
    }
}
