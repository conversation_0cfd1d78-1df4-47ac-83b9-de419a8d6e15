package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjRyjbxxFjxx;
import com.hl.orasync.domain.VWjRyjbxxFjxxToAuxiliaryPoliceInfoMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__606;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__606.class,
    uses = {ConversionUtils.class,VWjRyjbxxFjxxToAuxiliaryPoliceInfoMapper.class},
    imports = {}
)
public interface AuxiliaryPoliceInfoToVWjRyjbxxFjxxMapper extends BaseMapper<AuxiliaryPoliceInfo, VWjRyjbxxFjxx> {
  @Mapping(
      target = "csrq",
      source = "birthDate"
  )
  @Mapping(
      target = "zdbs",
      source = "medicalHistory"
  )
  @Mapping(
      target = "lwgs",
      source = "laborCompany"
  )
  @Mapping(
      target = "dwmc",
      source = "organization"
  )
  @Mapping(
      target = "zrldjh",
      source = "leaderPoliceNumber"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "zjWx",
      source = "officePhoneOuter"
  )
  @Mapping(
      target = "fzgl",
      source = "auxiliarySeniority"
  )
  @Mapping(
      target = "zrldxm",
      source = "leaderName"
  )
  @Mapping(
      target = "jz",
      source = "driverLicense"
  )
  @Mapping(
      target = "scsj",
      source = "firstAuxiliaryDate"
  )
  @Mapping(
      target = "cjmc",
      source = "hierarchyLevel"
  )
  @Mapping(
      target = "jzd",
      source = "residenceAddress"
  )
  @Mapping(
      target = "zytc",
      source = "specialty"
  )
  @Mapping(
      target = "zzmm",
      source = "politicalStatus"
  )
  @Mapping(
      target = "sfby",
      source = "militaryService"
  )
  @Mapping(
      target = "rzztmc",
      source = "employmentStatus"
  )
  @Mapping(
      target = "gh",
      source = "employeeNumber"
  )
  @Mapping(
      target = "jgmc",
      source = "nativePlace"
  )
  @Mapping(
      target = "fjgzrq",
      source = "startAuxiliaryDate"
  )
  @Mapping(
      target = "zjNx",
      source = "officePhoneInner"
  )
  @Mapping(
      target = "bzqdmc",
      source = "securityChannel"
  )
  @Mapping(
      target = "hjdz",
      source = "registeredAddress"
  )
  @Mapping(
      target = "sjhm",
      source = "phoneNumber"
  )
  @Mapping(
      target = "zc",
      source = "professionalTitle"
  )
  @Mapping(
      target = "xb",
      source = "gender"
  )
  @Mapping(
      target = "mz",
      source = "ethnicity"
  )
  @Mapping(
      target = "xxmc",
      source = "bloodType"
  )
  @Mapping(
      target = "hyzk",
      source = "maritalStatus"
  )
  @Mapping(
      target = "xl",
      source = "educationLevel"
  )
  @Mapping(
      target = "xm",
      source = "name"
  )
  @Mapping(
      target = "gwmc",
      source = "position"
  )
  @Mapping(
      target = "jkzkmc",
      source = "healthStatus"
  )
  @Mapping(
      target = "cym",
      source = "formerName"
  )
  @Mapping(
      target = "scgzsj",
      source = "firstWorkDate"
  )
  @Mapping(
      target = "rdsj",
      source = "partyJoinDate"
  )
  @Mapping(
      target = "nl",
      source = "age"
  )
  VWjRyjbxxFjxx convert(AuxiliaryPoliceInfo source);

  @Mapping(
      target = "csrq",
      source = "birthDate"
  )
  @Mapping(
      target = "zdbs",
      source = "medicalHistory"
  )
  @Mapping(
      target = "lwgs",
      source = "laborCompany"
  )
  @Mapping(
      target = "dwmc",
      source = "organization"
  )
  @Mapping(
      target = "zrldjh",
      source = "leaderPoliceNumber"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "zjWx",
      source = "officePhoneOuter"
  )
  @Mapping(
      target = "fzgl",
      source = "auxiliarySeniority"
  )
  @Mapping(
      target = "zrldxm",
      source = "leaderName"
  )
  @Mapping(
      target = "jz",
      source = "driverLicense"
  )
  @Mapping(
      target = "scsj",
      source = "firstAuxiliaryDate"
  )
  @Mapping(
      target = "cjmc",
      source = "hierarchyLevel"
  )
  @Mapping(
      target = "jzd",
      source = "residenceAddress"
  )
  @Mapping(
      target = "zytc",
      source = "specialty"
  )
  @Mapping(
      target = "zzmm",
      source = "politicalStatus"
  )
  @Mapping(
      target = "sfby",
      source = "militaryService"
  )
  @Mapping(
      target = "rzztmc",
      source = "employmentStatus"
  )
  @Mapping(
      target = "gh",
      source = "employeeNumber"
  )
  @Mapping(
      target = "jgmc",
      source = "nativePlace"
  )
  @Mapping(
      target = "fjgzrq",
      source = "startAuxiliaryDate"
  )
  @Mapping(
      target = "zjNx",
      source = "officePhoneInner"
  )
  @Mapping(
      target = "bzqdmc",
      source = "securityChannel"
  )
  @Mapping(
      target = "hjdz",
      source = "registeredAddress"
  )
  @Mapping(
      target = "sjhm",
      source = "phoneNumber"
  )
  @Mapping(
      target = "zc",
      source = "professionalTitle"
  )
  @Mapping(
      target = "xb",
      source = "gender"
  )
  @Mapping(
      target = "mz",
      source = "ethnicity"
  )
  @Mapping(
      target = "xxmc",
      source = "bloodType"
  )
  @Mapping(
      target = "hyzk",
      source = "maritalStatus"
  )
  @Mapping(
      target = "xl",
      source = "educationLevel"
  )
  @Mapping(
      target = "xm",
      source = "name"
  )
  @Mapping(
      target = "gwmc",
      source = "position"
  )
  @Mapping(
      target = "jkzkmc",
      source = "healthStatus"
  )
  @Mapping(
      target = "cym",
      source = "formerName"
  )
  @Mapping(
      target = "scgzsj",
      source = "firstWorkDate"
  )
  @Mapping(
      target = "rdsj",
      source = "partyJoinDate"
  )
  @Mapping(
      target = "nl",
      source = "age"
  )
  VWjRyjbxxFjxx convert(AuxiliaryPoliceInfo source, @MappingTarget VWjRyjbxxFjxx target);
}
