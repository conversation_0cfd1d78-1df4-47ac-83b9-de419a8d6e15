package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjYjbb;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-26T09:36:52+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceDrinkReportToVWjYjbbMapperImpl implements PoliceDrinkReportToVWjYjbbMapper {

    @Override
    public VWjYjbb convert(PoliceDrinkReport source) {
        if ( source == null ) {
            return null;
        }

        VWjYjbb vWjYjbb = new VWjYjbb();

        vWjYjbb.setDwbm( source.getOrganizationId() );
        vWjYjbb.setXm( source.getName() );
        vWjYjbb.setYyrxm( source.getInviter() );
        vWjYjbb.setGmsfhm( source.getIdCard() );
        vWjYjbb.setShjg( source.getApproveResult() );
        vWjYjbb.setBz( source.getRemark() );
        if ( source.getDrinkTime() != null ) {
            vWjYjbb.setYjrq( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getDrinkTime() ) );
        }
        vWjYjbb.setCyrs( source.getParticipants() );
        vWjYjbb.setYjdd( source.getLocation() );
        vWjYjbb.setCxfs( source.getTravelMode() );
        vWjYjbb.setFkrxm( source.getPayer() );
        vWjYjbb.setXxzjbh( source.getXxzj() );
        vWjYjbb.setYjsy( source.getReason() );

        return vWjYjbb;
    }

    @Override
    public VWjYjbb convert(PoliceDrinkReport source, VWjYjbb target) {
        if ( source == null ) {
            return target;
        }

        target.setDwbm( source.getOrganizationId() );
        target.setXm( source.getName() );
        target.setYyrxm( source.getInviter() );
        target.setGmsfhm( source.getIdCard() );
        target.setShjg( source.getApproveResult() );
        target.setBz( source.getRemark() );
        if ( source.getDrinkTime() != null ) {
            target.setYjrq( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getDrinkTime() ) );
        }
        else {
            target.setYjrq( null );
        }
        target.setCyrs( source.getParticipants() );
        target.setYjdd( source.getLocation() );
        target.setCxfs( source.getTravelMode() );
        target.setFkrxm( source.getPayer() );
        target.setXxzjbh( source.getXxzj() );
        target.setYjsy( source.getReason() );

        return target;
    }
}
