package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjRyxlxw;
import com.hl.orasync.domain.VWjRyxlxwToPoliceEducationMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__606;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__606.class,
    uses = {ConversionUtils.class,VWjRyxlxwToPoliceEducationMapper.class},
    imports = {}
)
public interface PoliceEducationToVWjRyxlxwMapper extends BaseMapper<PoliceEducation, VWjRyxlxw> {
  @Mapping(
      target = "bysj",
      source = "graduationDate"
  )
  @Mapping(
      target = "xl",
      source = "educationLevel"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "rxsj",
      source = "enrollmentDate"
  )
  @Mapping(
      target = "zymc",
      source = "majorName"
  )
  @Mapping(
      target = "xxmc",
      source = "schoolName"
  )
  VWjRyxlxw convert(PoliceEducation source);

  @Mapping(
      target = "bysj",
      source = "graduationDate"
  )
  @Mapping(
      target = "xl",
      source = "educationLevel"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "rxsj",
      source = "enrollmentDate"
  )
  @Mapping(
      target = "zymc",
      source = "majorName"
  )
  @Mapping(
      target = "xxmc",
      source = "schoolName"
  )
  VWjRyxlxw convert(PoliceEducation source, @MappingTarget VWjRyxlxw target);
}
