package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjQtFcqk;
import com.hl.orasync.domain.VWjQtFcqkToPoliceFamilyRealEstateMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__604;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__604.class,
    uses = {ConversionUtils.class,VWjQtFcqkToPoliceFamilyRealEstateMapper.class},
    imports = {}
)
public interface PoliceFamilyRealEstateToVWjQtFcqkMapper extends BaseMapper<PoliceFamilyRealEstate, VWjQtFcqk> {
  @Mapping(
      target = "xmCqr",
      source = "propertyOwnerName"
  )
  @Mapping(
      target = "csjg",
      source = "salePrice"
  )
  @Mapping(
      target = "jysj",
      source = "transactionDate"
  )
  @Mapping(
      target = "fclymc",
      source = "propertySource"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "dz",
      source = "propertyAddress"
  )
  @Mapping(
      target = "cssj",
      source = "saleDate"
  )
  @Mapping(
      target = "fclxmc",
      source = "propertyType"
  )
  @Mapping(
      target = "fcqxmc",
      source = "propertyDisposition"
  )
  @Mapping(
      target = "jyjg",
      source = "transactionPrice"
  )
  @Mapping(
      target = "jzmj",
      source = "buildingArea"
  )
  VWjQtFcqk convert(PoliceFamilyRealEstate source);

  @Mapping(
      target = "xmCqr",
      source = "propertyOwnerName"
  )
  @Mapping(
      target = "csjg",
      source = "salePrice"
  )
  @Mapping(
      target = "jysj",
      source = "transactionDate"
  )
  @Mapping(
      target = "fclymc",
      source = "propertySource"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "dz",
      source = "propertyAddress"
  )
  @Mapping(
      target = "cssj",
      source = "saleDate"
  )
  @Mapping(
      target = "fclxmc",
      source = "propertyType"
  )
  @Mapping(
      target = "fcqxmc",
      source = "propertyDisposition"
  )
  @Mapping(
      target = "jyjg",
      source = "transactionPrice"
  )
  @Mapping(
      target = "jzmj",
      source = "buildingArea"
  )
  VWjQtFcqk convert(PoliceFamilyRealEstate source, @MappingTarget VWjQtFcqk target);
}
