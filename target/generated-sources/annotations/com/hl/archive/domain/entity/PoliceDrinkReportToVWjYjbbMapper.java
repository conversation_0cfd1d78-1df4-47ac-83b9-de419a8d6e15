package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjYjbb;
import com.hl.orasync.domain.VWjYjbbToPoliceDrinkReportMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__604;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__604.class,
    uses = {ConversionUtils.class,VWjYjbbToPoliceDrinkReportMapper.class},
    imports = {}
)
public interface PoliceDrinkReportToVWjYjbbMapper extends BaseMapper<PoliceDrinkReport, VWjYjbb> {
  @Mapping(
      target = "dwbm",
      source = "organizationId"
  )
  @Mapping(
      target = "xm",
      source = "name"
  )
  @Mapping(
      target = "yyrxm",
      source = "inviter"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "shjg",
      source = "approveResult"
  )
  @Mapping(
      target = "bz",
      source = "remark"
  )
  @Mapping(
      target = "yjrq",
      source = "drinkTime"
  )
  @Mapping(
      target = "cyrs",
      source = "participants"
  )
  @Mapping(
      target = "yjdd",
      source = "location"
  )
  @Mapping(
      target = "cxfs",
      source = "travelMode"
  )
  @Mapping(
      target = "fkrxm",
      source = "payer"
  )
  @Mapping(
      target = "xxzjbh",
      source = "xxzj"
  )
  @Mapping(
      target = "yjsy",
      source = "reason"
  )
  VWjYjbb convert(PoliceDrinkReport source);

  @Mapping(
      target = "dwbm",
      source = "organizationId"
  )
  @Mapping(
      target = "xm",
      source = "name"
  )
  @Mapping(
      target = "yyrxm",
      source = "inviter"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "shjg",
      source = "approveResult"
  )
  @Mapping(
      target = "bz",
      source = "remark"
  )
  @Mapping(
      target = "yjrq",
      source = "drinkTime"
  )
  @Mapping(
      target = "cyrs",
      source = "participants"
  )
  @Mapping(
      target = "yjdd",
      source = "location"
  )
  @Mapping(
      target = "cxfs",
      source = "travelMode"
  )
  @Mapping(
      target = "fkrxm",
      source = "payer"
  )
  @Mapping(
      target = "xxzjbh",
      source = "xxzj"
  )
  @Mapping(
      target = "yjsy",
      source = "reason"
  )
  VWjYjbb convert(PoliceDrinkReport source, @MappingTarget VWjYjbb target);
}
