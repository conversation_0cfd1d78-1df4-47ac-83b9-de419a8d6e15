package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjZnYjqk;
import com.hl.orasync.domain.VWjZnYjqkToPoliceFamilyOverseasMigrationMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__604;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__604.class,
    uses = {ConversionUtils.class,VWjZnYjqkToPoliceFamilyOverseasMigrationMapper.class},
    imports = {}
)
public interface PoliceFamilyOverseasMigrationToVWjZnYjqkMapper extends BaseMapper<PoliceFamilyOverseasMigration, VWjZnYjqk> {
  @Mapping(
      target = "yjsj",
      source = "basisDate"
  )
  @Mapping(
      target = "xmPozn",
      source = "familyMemberName"
  )
  @Mapping(
      target = "yjlx",
      source = "migrationCategory"
  )
  @Mapping(
      target = "xjzcs",
      source = "currentCity"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "bz",
      source = "remarks"
  )
  @Mapping(
      target = "yjgj",
      source = "migrationCountry"
  )
  @Mapping(
      target = "yjzjhm",
      source = "migrationDocumentNumber"
  )
  VWjZnYjqk convert(PoliceFamilyOverseasMigration source);

  @Mapping(
      target = "yjsj",
      source = "basisDate"
  )
  @Mapping(
      target = "xmPozn",
      source = "familyMemberName"
  )
  @Mapping(
      target = "yjlx",
      source = "migrationCategory"
  )
  @Mapping(
      target = "xjzcs",
      source = "currentCity"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "bz",
      source = "remarks"
  )
  @Mapping(
      target = "yjgj",
      source = "migrationCountry"
  )
  @Mapping(
      target = "yjzjhm",
      source = "migrationDocumentNumber"
  )
  VWjZnYjqk convert(PoliceFamilyOverseasMigration source, @MappingTarget VWjZnYjqk target);
}
