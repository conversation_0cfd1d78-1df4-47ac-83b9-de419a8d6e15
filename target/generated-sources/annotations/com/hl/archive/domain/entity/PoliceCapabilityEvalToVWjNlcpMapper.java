package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjNlcp;
import com.hl.orasync.domain.VWjNlcpToPoliceCapabilityEvalMapper;
import io.github.linpeilie.AutoMapperConfig__606;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__606.class,
    uses = {VWjNlcpToPoliceCapabilityEvalMapper.class},
    imports = {}
)
public interface PoliceCapabilityEvalToVWjNlcpMapper extends BaseMapper<PoliceCapabilityEval, VWjNlcp> {
  @Mapping(
      target = "sqrxm",
      source = "participantName"
  )
  @Mapping(
      target = "shjgmc",
      source = "reviewResult"
  )
  @Mapping(
      target = "lcid",
      source = "lcid"
  )
  @Mapping(
      target = "dlmc",
      source = "categoryName"
  )
  @Mapping(
      target = "dwmc",
      source = "orgName"
  )
  @Mapping(
      target = "shrxm",
      source = "reviewer"
  )
  @Mapping(
      target = "fszt",
      source = "evalStatus"
  )
  @Mapping(
      target = "zwmc",
      source = "position"
  )
  @Mapping(
      target = "bqzMc",
      source = "evalLevel"
  )
  @Mapping(
      target = "bqMc",
      source = "featureName"
  )
  @Mapping(
      target = "famc",
      source = "planName"
  )
  @Mapping(
      target = "jh",
      source = "policeNumber"
  )
  @Mapping(
      target = "xxzjbh",
      source = "xxzjbh"
  )
  VWjNlcp convert(PoliceCapabilityEval source);

  @Mapping(
      target = "sqrxm",
      source = "participantName"
  )
  @Mapping(
      target = "shjgmc",
      source = "reviewResult"
  )
  @Mapping(
      target = "lcid",
      source = "lcid"
  )
  @Mapping(
      target = "dlmc",
      source = "categoryName"
  )
  @Mapping(
      target = "dwmc",
      source = "orgName"
  )
  @Mapping(
      target = "shrxm",
      source = "reviewer"
  )
  @Mapping(
      target = "fszt",
      source = "evalStatus"
  )
  @Mapping(
      target = "zwmc",
      source = "position"
  )
  @Mapping(
      target = "bqzMc",
      source = "evalLevel"
  )
  @Mapping(
      target = "bqMc",
      source = "featureName"
  )
  @Mapping(
      target = "famc",
      source = "planName"
  )
  @Mapping(
      target = "jh",
      source = "policeNumber"
  )
  @Mapping(
      target = "xxzjbh",
      source = "xxzjbh"
  )
  VWjNlcp convert(PoliceCapabilityEval source, @MappingTarget VWjNlcp target);
}
