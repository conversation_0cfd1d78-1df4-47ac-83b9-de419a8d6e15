package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjBzjlGrry;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-26T13:43:24+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceHonorsToVWjBzjlGrryMapperImpl implements PoliceHonorsToVWjBzjlGrryMapper {

    @Override
    public VWjBzjlGrry convert(PoliceHonors source) {
        if ( source == null ) {
            return null;
        }

        VWjBzjlGrry vWjBzjlGrry = new VWjBzjlGrry();

        vWjBzjlGrry.setJljgmc( source.getApproveAuthority() );
        if ( source.getAwardDate() != null ) {
            vWjBzjlGrry.setBzsj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getAwardDate() ) );
        }
        vWjBzjlGrry.setGmsfhm( source.getIdCard() );
        vWjBzjlGrry.setDwmc( source.getOrganizationName() );
        vWjBzjlGrry.setRyjbmc( source.getHonorLevel() );
        vWjBzjlGrry.setBzwh( source.getAwardDocNo() );
        vWjBzjlGrry.setJlmc( source.getHonorName() );
        vWjBzjlGrry.setXxzjbh( source.getBh() );

        return vWjBzjlGrry;
    }

    @Override
    public VWjBzjlGrry convert(PoliceHonors source, VWjBzjlGrry target) {
        if ( source == null ) {
            return target;
        }

        target.setJljgmc( source.getApproveAuthority() );
        if ( source.getAwardDate() != null ) {
            target.setBzsj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getAwardDate() ) );
        }
        else {
            target.setBzsj( null );
        }
        target.setGmsfhm( source.getIdCard() );
        target.setDwmc( source.getOrganizationName() );
        target.setRyjbmc( source.getHonorLevel() );
        target.setBzwh( source.getAwardDocNo() );
        target.setJlmc( source.getHonorName() );
        target.setXxzjbh( source.getBh() );

        return target;
    }
}
