package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjXhjhRxgr;
import com.hl.orasync.domain.VWjXhjhRxgrToPoliceProjectEntryPersonMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__604;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__604.class,
    uses = {ConversionUtils.class,VWjXhjhRxgrToPoliceProjectEntryPersonMapper.class},
    imports = {}
)
public interface PoliceProjectEntryPersonToVWjXhjhRxgrMapper extends BaseMapper<PoliceProjectEntryPerson, VWjXhjhRxgr> {
  @Mapping(
      target = "djsj",
      source = "entryTime"
  )
  @Mapping(
      target = "shztmc",
      source = "auditStatus"
  )
  @Mapping(
      target = "xm",
      source = "name"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "ryjbmc",
      source = "honorLevel"
  )
  @Mapping(
      target = "dwmc",
      source = "unit"
  )
  @Mapping(
      target = "zgrymc",
      source = "honorName"
  )
  @Mapping(
      target = "pylxrXm",
      source = "contactPerson"
  )
  @Mapping(
      target = "jh",
      source = "policeNumber"
  )
  @Mapping(
      target = "xxzjbh",
      source = "zjbh"
  )
  VWjXhjhRxgr convert(PoliceProjectEntryPerson source);

  @Mapping(
      target = "djsj",
      source = "entryTime"
  )
  @Mapping(
      target = "shztmc",
      source = "auditStatus"
  )
  @Mapping(
      target = "xm",
      source = "name"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "ryjbmc",
      source = "honorLevel"
  )
  @Mapping(
      target = "dwmc",
      source = "unit"
  )
  @Mapping(
      target = "zgrymc",
      source = "honorName"
  )
  @Mapping(
      target = "pylxrXm",
      source = "contactPerson"
  )
  @Mapping(
      target = "jh",
      source = "policeNumber"
  )
  @Mapping(
      target = "xxzjbh",
      source = "zjbh"
  )
  VWjXhjhRxgr convert(PoliceProjectEntryPerson source, @MappingTarget VWjXhjhRxgr target);
}
