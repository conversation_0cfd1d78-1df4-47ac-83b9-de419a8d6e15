package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceProjectMaterial;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-26T13:43:23+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjXhjhRxgrTwzlToPoliceProjectMaterialMapperImpl implements VWjXhjhRxgrTwzlToPoliceProjectMaterialMapper {

    @Override
    public PoliceProjectMaterial convert(VWjXhjhRxgrTwzl source) {
        if ( source == null ) {
            return null;
        }

        PoliceProjectMaterial policeProjectMaterial = new PoliceProjectMaterial();

        policeProjectMaterial.setImageName( source.getTpzlBt() );
        policeProjectMaterial.setImageUrl( source.getTpzlNr() );
        policeProjectMaterial.setXhjhZjbh( source.getJlXxzjbh() );
        policeProjectMaterial.setZjbh( source.getXxzjbh() );

        return policeProjectMaterial;
    }

    @Override
    public PoliceProjectMaterial convert(VWjXhjhRxgrTwzl source, PoliceProjectMaterial target) {
        if ( source == null ) {
            return target;
        }

        target.setImageName( source.getTpzlBt() );
        target.setImageUrl( source.getTpzlNr() );
        target.setXhjhZjbh( source.getJlXxzjbh() );
        target.setZjbh( source.getXxzjbh() );

        return target;
    }
}
