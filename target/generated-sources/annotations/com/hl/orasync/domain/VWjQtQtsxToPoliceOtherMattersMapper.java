package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceOtherMatters;
import com.hl.archive.domain.entity.PoliceOtherMattersToVWjQtQtsxMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__607;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__607.class,
    uses = {ConversionUtils.class,PoliceOtherMattersToVWjQtQtsxMapper.class},
    imports = {}
)
public interface VWjQtQtsxToPoliceOtherMattersMapper extends BaseMapper<VWjQtQtsx, PoliceOtherMatters> {
  @Mapping(
      target = "reportContent",
      source = "bz"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "registrationDate",
      source = "djrq",
      qualifiedByName = {"strToDate"}
  )
  PoliceOtherMatters convert(VWjQtQtsx source);

  @Mapping(
      target = "reportContent",
      source = "bz"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "registrationDate",
      source = "djrq",
      qualifiedByName = {"strToDate"}
  )
  PoliceOtherMatters convert(VWjQtQtsx source, @MappingTarget PoliceOtherMatters target);
}
