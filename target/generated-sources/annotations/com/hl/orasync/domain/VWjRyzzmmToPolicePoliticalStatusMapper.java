package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PolicePoliticalStatus;
import com.hl.archive.domain.entity.PolicePoliticalStatusToVWjRyzzmmMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__607;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__607.class,
    uses = {ConversionUtils.class,PolicePoliticalStatusToVWjRyzzmmMapper.class},
    imports = {}
)
public interface VWjRyzzmmToPolicePoliticalStatusMapper extends BaseMapper<VWjRyzzmm, PolicePoliticalStatus> {
  @Mapping(
      target = "joinPartyDate",
      source = "cjdpsj",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "politicalIdentity",
      source = "zzsf"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  PolicePoliticalStatus convert(VWjRyzzmm source);

  @Mapping(
      target = "joinPartyDate",
      source = "cjdpsj",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "politicalIdentity",
      source = "zzsf"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  PolicePoliticalStatus convert(VWjRyzzmm source, @MappingTarget PolicePoliticalStatus target);
}
