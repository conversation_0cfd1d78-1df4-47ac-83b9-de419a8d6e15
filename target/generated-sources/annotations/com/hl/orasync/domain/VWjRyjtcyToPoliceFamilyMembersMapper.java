package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceFamilyMembers;
import com.hl.archive.domain.entity.PoliceFamilyMembersToVWjRyjtcyMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__605;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__605.class,
    uses = {ConversionUtils.class,PoliceFamilyMembersToVWjRyjtcyMapper.class},
    imports = {}
)
public interface VWjRyjtcyToPoliceFamilyMembersMapper extends BaseMapper<VWjRyjtcy, PoliceFamilyMembers> {
  @Mapping(
      target = "mobilePhone",
      source = "sjhm"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "politicalStatus",
      source = "zzmm"
  )
  @Mapping(
      target = "workUnitPosition",
      source = "gzdw"
  )
  @Mapping(
      target = "memberName",
      source = "xm"
  )
  @Mapping(
      target = "relationship",
      source = "rygx"
  )
  @Mapping(
      target = "birthDate",
      source = "csrq"
  )
  PoliceFamilyMembers convert(VWjRyjtcy source);

  @Mapping(
      target = "mobilePhone",
      source = "sjhm"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "politicalStatus",
      source = "zzmm"
  )
  @Mapping(
      target = "workUnitPosition",
      source = "gzdw"
  )
  @Mapping(
      target = "memberName",
      source = "xm"
  )
  @Mapping(
      target = "relationship",
      source = "rygx"
  )
  @Mapping(
      target = "birthDate",
      source = "csrq"
  )
  PoliceFamilyMembers convert(VWjRyjtcy source, @MappingTarget PoliceFamilyMembers target);
}
