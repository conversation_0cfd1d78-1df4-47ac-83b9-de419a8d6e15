package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceTrainingRecords;
import com.hl.archive.domain.entity.PoliceTrainingRecordsToVWjXldaMapper;
import io.github.linpeilie.AutoMapperConfig__606;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__606.class,
    uses = {PoliceTrainingRecordsToVWjXldaMapper.class},
    imports = {}
)
public interface VWjXldaToPoliceTrainingRecordsMapper extends BaseMapper<VWjXlda, PoliceTrainingRecords> {
  @Mapping(
      target = "score",
      source = "pfdf"
  )
  @Mapping(
      target = "trainingTime",
      source = "pxsj"
  )
  @Mapping(
      target = "examProjectName",
      source = "kpxmmc"
  )
  @Mapping(
      target = "trainingName",
      source = "pxbmc"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "grade",
      source = "cj"
  )
  PoliceTrainingRecords convert(VWjXlda source);

  @Mapping(
      target = "score",
      source = "pfdf"
  )
  @Mapping(
      target = "trainingTime",
      source = "pxsj"
  )
  @Mapping(
      target = "examProjectName",
      source = "kpxmmc"
  )
  @Mapping(
      target = "trainingName",
      source = "pxbmc"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "grade",
      source = "cj"
  )
  PoliceTrainingRecords convert(VWjXlda source, @MappingTarget PoliceTrainingRecords target);
}
