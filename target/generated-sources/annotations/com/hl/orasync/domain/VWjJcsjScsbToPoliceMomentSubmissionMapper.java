package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceMomentSubmission;
import com.hl.archive.domain.entity.PoliceMomentSubmissionToVWjJcsjScsbMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__606;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__606.class,
    uses = {ConversionUtils.class,PoliceMomentSubmissionToVWjJcsjScsbMapper.class},
    imports = {}
)
public interface VWjJcsjScsbToPoliceMomentSubmissionMapper extends BaseMapper<VWjJcsjScsb, PoliceMomentSubmission> {
  @Mapping(
      target = "submissionType",
      source = "bslxmc"
  )
  @Mapping(
      target = "submitter",
      source = "bsrxm"
  )
  @Mapping(
      target = "materialType",
      source = "cllxmc"
  )
  @Mapping(
      target = "officerName",
      source = "cjmj"
  )
  @Mapping(
      target = "submitUnit",
      source = "bsdwmc"
  )
  @Mapping(
      target = "auditResult",
      source = "shjgmc"
  )
  @Mapping(
      target = "materialIntro",
      source = "scjj"
  )
  @Mapping(
      target = "submissionTime",
      source = "djsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "zjbh",
      source = "xxzjbh"
  )
  PoliceMomentSubmission convert(VWjJcsjScsb source);

  @Mapping(
      target = "submissionType",
      source = "bslxmc"
  )
  @Mapping(
      target = "submitter",
      source = "bsrxm"
  )
  @Mapping(
      target = "materialType",
      source = "cllxmc"
  )
  @Mapping(
      target = "officerName",
      source = "cjmj"
  )
  @Mapping(
      target = "submitUnit",
      source = "bsdwmc"
  )
  @Mapping(
      target = "auditResult",
      source = "shjgmc"
  )
  @Mapping(
      target = "materialIntro",
      source = "scjj"
  )
  @Mapping(
      target = "submissionTime",
      source = "djsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "zjbh",
      source = "xxzjbh"
  )
  PoliceMomentSubmission convert(VWjJcsjScsb source, @MappingTarget PoliceMomentSubmission target);
}
