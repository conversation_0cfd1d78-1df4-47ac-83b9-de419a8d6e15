package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceInvestmentInfo;
import com.hl.archive.domain.entity.PoliceInvestmentInfoToVWjBrTzqkMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__606;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__606.class,
    uses = {ConversionUtils.class,PoliceInvestmentInfoToVWjBrTzqkMapper.class},
    imports = {}
)
public interface VWjBrTzqkToPoliceInvestmentInfoMapper extends BaseMapper<VWjBrTzqk, PoliceInvestmentInfo> {
  @Mapping(
      target = "investmentEntity",
      source = "tzcpmc"
  )
  @Mapping(
      target = "investmentAmount",
      source = "je",
      qualifiedByName = {"strToBigDecimal"}
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "investmentSource",
      source = "tzqx"
  )
  @Mapping(
      target = "transactionDate",
      source = "tzsj",
      qualifiedByName = {"strToDate"}
  )
  PoliceInvestmentInfo convert(VWjBrTzqk source);

  @Mapping(
      target = "investmentEntity",
      source = "tzcpmc"
  )
  @Mapping(
      target = "investmentAmount",
      source = "je",
      qualifiedByName = {"strToBigDecimal"}
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "investmentSource",
      source = "tzqx"
  )
  @Mapping(
      target = "transactionDate",
      source = "tzsj",
      qualifiedByName = {"strToDate"}
  )
  PoliceInvestmentInfo convert(VWjBrTzqk source, @MappingTarget PoliceInvestmentInfo target);
}
