package com.hl.orasync.domain;

import com.hl.archive.domain.entity.AuxiliaryPoliceInfo;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-26T13:29:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjRyjbxxFjxxToAuxiliaryPoliceInfoMapperImpl implements VWjRyjbxxFjxxToAuxiliaryPoliceInfoMapper {

    @Override
    public AuxiliaryPoliceInfo convert(VWjRyjbxxFjxx source) {
        if ( source == null ) {
            return null;
        }

        AuxiliaryPoliceInfo auxiliaryPoliceInfo = new AuxiliaryPoliceInfo();

        auxiliaryPoliceInfo.setSpecialty( source.getZytc() );
        auxiliaryPoliceInfo.setEthnicity( source.getMz() );
        auxiliaryPoliceInfo.setGender( source.getXb() );
        auxiliaryPoliceInfo.setIdCard( source.getGmsfhm() );
        auxiliaryPoliceInfo.setHierarchyLevel( source.getCjmc() );
        auxiliaryPoliceInfo.setMilitaryService( source.getSfby() );
        auxiliaryPoliceInfo.setBloodType( source.getXxmc() );
        auxiliaryPoliceInfo.setEmploymentStatus( source.getRzztmc() );
        auxiliaryPoliceInfo.setEmployeeNumber( source.getGh() );
        auxiliaryPoliceInfo.setRegisteredAddress( source.getHjdz() );
        auxiliaryPoliceInfo.setEducationLevel( source.getXl() );
        auxiliaryPoliceInfo.setDriverLicense( source.getJz() );
        auxiliaryPoliceInfo.setProfessionalTitle( source.getZc() );
        auxiliaryPoliceInfo.setStartAuxiliaryDate( ConversionUtils.strToLocalDate( source.getFjgzrq() ) );
        auxiliaryPoliceInfo.setMedicalHistory( source.getZdbs() );
        auxiliaryPoliceInfo.setFormerName( source.getCym() );
        auxiliaryPoliceInfo.setFirstWorkDate( ConversionUtils.strToLocalDate( source.getScgzsj() ) );
        auxiliaryPoliceInfo.setPartyJoinDate( ConversionUtils.strToLocalDate( source.getRdsj() ) );
        auxiliaryPoliceInfo.setLaborCompany( source.getLwgs() );
        auxiliaryPoliceInfo.setOfficePhoneOuter( source.getZjWx() );
        auxiliaryPoliceInfo.setFirstAuxiliaryDate( ConversionUtils.strToLocalDate( source.getScsj() ) );
        auxiliaryPoliceInfo.setPoliticalStatus( source.getZzmm() );
        auxiliaryPoliceInfo.setBirthDate( ConversionUtils.strToLocalDate( source.getCsrq() ) );
        auxiliaryPoliceInfo.setOfficePhoneInner( source.getZjNx() );
        auxiliaryPoliceInfo.setPhoneNumber( source.getSjhm() );
        auxiliaryPoliceInfo.setHealthStatus( source.getJkzkmc() );
        auxiliaryPoliceInfo.setLeaderName( source.getZrldxm() );
        auxiliaryPoliceInfo.setLeaderPoliceNumber( source.getZrldjh() );
        auxiliaryPoliceInfo.setOrganization( source.getDwmc() );
        auxiliaryPoliceInfo.setName( source.getXm() );
        auxiliaryPoliceInfo.setNativePlace( source.getJgmc() );
        auxiliaryPoliceInfo.setSecurityChannel( source.getBzqdmc() );
        auxiliaryPoliceInfo.setPosition( source.getGwmc() );
        auxiliaryPoliceInfo.setAuxiliarySeniority( ConversionUtils.bigDecimalToString( source.getFzgl() ) );
        auxiliaryPoliceInfo.setResidenceAddress( source.getJzd() );
        auxiliaryPoliceInfo.setMaritalStatus( source.getHyzk() );
        auxiliaryPoliceInfo.setAge( ConversionUtils.bigDecimalToString( source.getNl() ) );

        return auxiliaryPoliceInfo;
    }

    @Override
    public AuxiliaryPoliceInfo convert(VWjRyjbxxFjxx source, AuxiliaryPoliceInfo target) {
        if ( source == null ) {
            return target;
        }

        target.setSpecialty( source.getZytc() );
        target.setEthnicity( source.getMz() );
        target.setGender( source.getXb() );
        target.setIdCard( source.getGmsfhm() );
        target.setHierarchyLevel( source.getCjmc() );
        target.setMilitaryService( source.getSfby() );
        target.setBloodType( source.getXxmc() );
        target.setEmploymentStatus( source.getRzztmc() );
        target.setEmployeeNumber( source.getGh() );
        target.setRegisteredAddress( source.getHjdz() );
        target.setEducationLevel( source.getXl() );
        target.setDriverLicense( source.getJz() );
        target.setProfessionalTitle( source.getZc() );
        target.setStartAuxiliaryDate( ConversionUtils.strToLocalDate( source.getFjgzrq() ) );
        target.setMedicalHistory( source.getZdbs() );
        target.setFormerName( source.getCym() );
        target.setFirstWorkDate( ConversionUtils.strToLocalDate( source.getScgzsj() ) );
        target.setPartyJoinDate( ConversionUtils.strToLocalDate( source.getRdsj() ) );
        target.setLaborCompany( source.getLwgs() );
        target.setOfficePhoneOuter( source.getZjWx() );
        target.setFirstAuxiliaryDate( ConversionUtils.strToLocalDate( source.getScsj() ) );
        target.setPoliticalStatus( source.getZzmm() );
        target.setBirthDate( ConversionUtils.strToLocalDate( source.getCsrq() ) );
        target.setOfficePhoneInner( source.getZjNx() );
        target.setPhoneNumber( source.getSjhm() );
        target.setHealthStatus( source.getJkzkmc() );
        target.setLeaderName( source.getZrldxm() );
        target.setLeaderPoliceNumber( source.getZrldjh() );
        target.setOrganization( source.getDwmc() );
        target.setName( source.getXm() );
        target.setNativePlace( source.getJgmc() );
        target.setSecurityChannel( source.getBzqdmc() );
        target.setPosition( source.getGwmc() );
        target.setAuxiliarySeniority( ConversionUtils.bigDecimalToString( source.getFzgl() ) );
        target.setResidenceAddress( source.getJzd() );
        target.setMaritalStatus( source.getHyzk() );
        target.setAge( ConversionUtils.bigDecimalToString( source.getNl() ) );

        return target;
    }
}
