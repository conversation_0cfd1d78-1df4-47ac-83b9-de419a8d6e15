package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceFamilyRealEstate;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-26T09:36:53+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjQtFcqkToPoliceFamilyRealEstateMapperImpl implements VWjQtFcqkToPoliceFamilyRealEstateMapper {

    @Override
    public PoliceFamilyRealEstate convert(VWjQtFcqk source) {
        if ( source == null ) {
            return null;
        }

        PoliceFamilyRealEstate policeFamilyRealEstate = new PoliceFamilyRealEstate();

        policeFamilyRealEstate.setPropertyDisposition( source.getFcqxmc() );
        policeFamilyRealEstate.setSalePrice( source.getCsjg() );
        policeFamilyRealEstate.setPropertyAddress( source.getDz() );
        policeFamilyRealEstate.setIdCard( source.getGmsfhm() );
        policeFamilyRealEstate.setPropertyType( source.getFclxmc() );
        policeFamilyRealEstate.setPropertySource( source.getFclymc() );
        policeFamilyRealEstate.setTransactionPrice( source.getJyjg() );
        policeFamilyRealEstate.setBuildingArea( source.getJzmj() );
        policeFamilyRealEstate.setSaleDate( ConversionUtils.strToDate( source.getCssj() ) );
        policeFamilyRealEstate.setPropertyOwnerName( source.getXmCqr() );
        policeFamilyRealEstate.setTransactionDate( ConversionUtils.strToDate( source.getJysj() ) );

        return policeFamilyRealEstate;
    }

    @Override
    public PoliceFamilyRealEstate convert(VWjQtFcqk source, PoliceFamilyRealEstate target) {
        if ( source == null ) {
            return target;
        }

        target.setPropertyDisposition( source.getFcqxmc() );
        target.setSalePrice( source.getCsjg() );
        target.setPropertyAddress( source.getDz() );
        target.setIdCard( source.getGmsfhm() );
        target.setPropertyType( source.getFclxmc() );
        target.setPropertySource( source.getFclymc() );
        target.setTransactionPrice( source.getJyjg() );
        target.setBuildingArea( source.getJzmj() );
        target.setSaleDate( ConversionUtils.strToDate( source.getCssj() ) );
        target.setPropertyOwnerName( source.getXmCqr() );
        target.setTransactionDate( ConversionUtils.strToDate( source.getJysj() ) );

        return target;
    }
}
