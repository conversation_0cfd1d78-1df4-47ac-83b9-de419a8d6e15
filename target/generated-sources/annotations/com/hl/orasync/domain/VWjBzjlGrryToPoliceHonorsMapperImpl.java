package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceHonors;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-26T09:36:53+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjBzjlGrryToPoliceHonorsMapperImpl implements VWjBzjlGrryToPoliceHonorsMapper {

    @Override
    public PoliceHonors convert(VWjBzjlGrry source) {
        if ( source == null ) {
            return null;
        }

        PoliceHonors policeHonors = new PoliceHonors();

        policeHonors.setApproveAuthority( source.getJljgmc() );
        policeHonors.setHonorLevel( source.getRyjbmc() );
        policeHonors.setOrganizationName( source.getDwmc() );
        policeHonors.setIdCard( source.getGmsfhm() );
        policeHonors.setBh( source.getXxzjbh() );
        policeHonors.setHonorName( source.getJlmc() );
        policeHonors.setAwardDate( ConversionUtils.strToLocalDate( source.getBzsj() ) );
        policeHonors.setAwardDocNo( source.getBzwh() );

        return policeHonors;
    }

    @Override
    public PoliceHonors convert(VWjBzjlGrry source, PoliceHonors target) {
        if ( source == null ) {
            return target;
        }

        target.setApproveAuthority( source.getJljgmc() );
        target.setHonorLevel( source.getRyjbmc() );
        target.setOrganizationName( source.getDwmc() );
        target.setIdCard( source.getGmsfhm() );
        target.setBh( source.getXxzjbh() );
        target.setHonorName( source.getJlmc() );
        target.setAwardDate( ConversionUtils.strToLocalDate( source.getBzsj() ) );
        target.setAwardDocNo( source.getBzwh() );

        return target;
    }
}
