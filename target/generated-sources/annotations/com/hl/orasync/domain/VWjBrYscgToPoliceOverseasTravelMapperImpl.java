package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceOverseasTravel;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-26T13:29:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjBrYscgToPoliceOverseasTravelMapperImpl implements VWjBrYscgToPoliceOverseasTravelMapper {

    @Override
    public PoliceOverseasTravel convert(VWjBrYscg source) {
        if ( source == null ) {
            return null;
        }

        PoliceOverseasTravel policeOverseasTravel = new PoliceOverseasTravel();

        policeOverseasTravel.setPassportNumber( source.getHzhm() );
        policeOverseasTravel.setEndDate( ConversionUtils.strToDate( source.getJssj() ) );
        policeOverseasTravel.setIdCard( source.getGmsfhm() );
        policeOverseasTravel.setTravelReason( source.getSy() );
        policeOverseasTravel.setApprovalAuthority( source.getSpjgmc() );
        policeOverseasTravel.setDestinationCountry( source.getSdgj() );
        policeOverseasTravel.setStartDate( ConversionUtils.strToDate( source.getKssj() ) );

        return policeOverseasTravel;
    }

    @Override
    public PoliceOverseasTravel convert(VWjBrYscg source, PoliceOverseasTravel target) {
        if ( source == null ) {
            return target;
        }

        target.setPassportNumber( source.getHzhm() );
        target.setEndDate( ConversionUtils.strToDate( source.getJssj() ) );
        target.setIdCard( source.getGmsfhm() );
        target.setTravelReason( source.getSy() );
        target.setApprovalAuthority( source.getSpjgmc() );
        target.setDestinationCountry( source.getSdgj() );
        target.setStartDate( ConversionUtils.strToDate( source.getKssj() ) );

        return target;
    }
}
