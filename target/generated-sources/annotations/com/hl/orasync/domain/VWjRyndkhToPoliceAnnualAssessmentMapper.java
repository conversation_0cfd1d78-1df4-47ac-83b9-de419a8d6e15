package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceAnnualAssessment;
import com.hl.archive.domain.entity.PoliceAnnualAssessmentToVWjRyndkhMapper;
import io.github.linpeilie.AutoMapperConfig__604;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__604.class,
    uses = {PoliceAnnualAssessmentToVWjRyndkhMapper.class},
    imports = {}
)
public interface VWjRyndkhToPoliceAnnualAssessmentMapper extends BaseMapper<VWjRyndkh, PoliceAnnualAssessment> {
  @Mapping(
      target = "assessmentResult",
      source = "khjg"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "assessmentCategory",
      source = "kclb"
  )
  @Mapping(
      target = "assessmentYear",
      source = "kcnd"
  )
  PoliceAnnualAssessment convert(VWjRyndkh source);

  @Mapping(
      target = "assessmentResult",
      source = "khjg"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "assessmentCategory",
      source = "kclb"
  )
  @Mapping(
      target = "assessmentYear",
      source = "kcnd"
  )
  PoliceAnnualAssessment convert(VWjRyndkh source, @MappingTarget PoliceAnnualAssessment target);
}
