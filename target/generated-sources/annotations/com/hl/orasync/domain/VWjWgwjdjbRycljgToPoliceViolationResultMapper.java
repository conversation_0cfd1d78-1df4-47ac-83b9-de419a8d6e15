package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceViolationResult;
import com.hl.archive.domain.entity.PoliceViolationResultToVWjWgwjdjbRycljgMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__607;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__607.class,
    uses = {ConversionUtils.class,PoliceViolationResultToVWjWgwjdjbRycljgMapper.class},
    imports = {}
)
public interface VWjWgwjdjbRycljgToPoliceViolationResultMapper extends BaseMapper<VWjWgwjdjbRycljg, PoliceViolationResult> {
  @Mapping(
      target = "cldw",
      source = "cldw"
  )
  @Mapping(
      target = "cljg",
      source = "cljg"
  )
  @Mapping(
      target = "ryXxzjbh",
      source = "ryXxzjbh"
  )
  @Mapping(
      target = "clsj",
      source = "clsj",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "lbmc",
      source = "lbmc"
  )
  @Mapping(
      target = "wtXxzjbh",
      source = "wtXxzjbh"
  )
  @Mapping(
      target = "xxzjbh",
      source = "xxzjbh"
  )
  PoliceViolationResult convert(VWjWgwjdjbRycljg source);

  @Mapping(
      target = "cldw",
      source = "cldw"
  )
  @Mapping(
      target = "cljg",
      source = "cljg"
  )
  @Mapping(
      target = "ryXxzjbh",
      source = "ryXxzjbh"
  )
  @Mapping(
      target = "clsj",
      source = "clsj",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "lbmc",
      source = "lbmc"
  )
  @Mapping(
      target = "wtXxzjbh",
      source = "wtXxzjbh"
  )
  @Mapping(
      target = "xxzjbh",
      source = "xxzjbh"
  )
  PoliceViolationResult convert(VWjWgwjdjbRycljg source,
      @MappingTarget PoliceViolationResult target);
}
