package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceFamilyOverseasMigration;
import com.hl.archive.domain.entity.PoliceFamilyOverseasMigrationToVWjZnYjqkMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__606;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__606.class,
    uses = {ConversionUtils.class,PoliceFamilyOverseasMigrationToVWjZnYjqkMapper.class},
    imports = {}
)
public interface VWjZnYjqkToPoliceFamilyOverseasMigrationMapper extends BaseMapper<VWjZnYjqk, PoliceFamilyOverseasMigration> {
  @Mapping(
      target = "migrationCountry",
      source = "yjgj"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "currentCity",
      source = "xjzcs"
  )
  @Mapping(
      target = "migrationDocumentNumber",
      source = "yjzjhm"
  )
  @Mapping(
      target = "migrationCategory",
      source = "yjlx"
  )
  @Mapping(
      target = "basisDate",
      source = "yjsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "remarks",
      source = "bz"
  )
  @Mapping(
      target = "familyMemberName",
      source = "xmPozn"
  )
  PoliceFamilyOverseasMigration convert(VWjZnYjqk source);

  @Mapping(
      target = "migrationCountry",
      source = "yjgj"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "currentCity",
      source = "xjzcs"
  )
  @Mapping(
      target = "migrationDocumentNumber",
      source = "yjzjhm"
  )
  @Mapping(
      target = "migrationCategory",
      source = "yjlx"
  )
  @Mapping(
      target = "basisDate",
      source = "yjsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "remarks",
      source = "bz"
  )
  @Mapping(
      target = "familyMemberName",
      source = "xmPozn"
  )
  PoliceFamilyOverseasMigration convert(VWjZnYjqk source,
      @MappingTarget PoliceFamilyOverseasMigration target);
}
