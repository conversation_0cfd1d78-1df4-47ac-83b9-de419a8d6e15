package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceDishonestExecutor;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-26T10:29:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjQtPosxbzxrToPoliceDishonestExecutorMapperImpl implements VWjQtPosxbzxrToPoliceDishonestExecutorMapper {

    @Override
    public PoliceDishonestExecutor convert(VWjQtPosxbzxr source) {
        if ( source == null ) {
            return null;
        }

        PoliceDishonestExecutor policeDishonestExecutor = new PoliceDishonestExecutor();

        policeDishonestExecutor.setDishonestReason( source.getJtqk() );
        policeDishonestExecutor.setIdCard( source.getGmsfhm() );
        policeDishonestExecutor.setExecutionUnit( source.getZxjg() );
        policeDishonestExecutor.setRelationship( source.getRygxmc() );

        return policeDishonestExecutor;
    }

    @Override
    public PoliceDishonestExecutor convert(VWjQtPosxbzxr source, PoliceDishonestExecutor target) {
        if ( source == null ) {
            return target;
        }

        target.setDishonestReason( source.getJtqk() );
        target.setIdCard( source.getGmsfhm() );
        target.setExecutionUnit( source.getZxjg() );
        target.setRelationship( source.getRygxmc() );

        return target;
    }
}
