package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceRankInfo;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-26T09:36:52+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjRyjxxxToPoliceRankInfoMapperImpl implements VWjRyjxxxToPoliceRankInfoMapper {

    @Override
    public PoliceRankInfo convert(VWjRyjxxx source) {
        if ( source == null ) {
            return null;
        }

        PoliceRankInfo policeRankInfo = new PoliceRankInfo();

        policeRankInfo.setRankType( source.getSxzl() );
        policeRankInfo.setPromotionReason( source.getSxyy() );
        policeRankInfo.setIdCard( source.getGmsfhm() );
        policeRankInfo.setRankEndDate( ConversionUtils.strToDate( source.getXczzrq() ) );
        policeRankInfo.setPromotionDate( ConversionUtils.strToDate( source.getSxsj() ) );
        policeRankInfo.setRankStartDate( ConversionUtils.strToDate( source.getXcqsrq() ) );
        policeRankInfo.setAdminLevelAtPromotion( source.getSxsxzzj() );
        policeRankInfo.setRankTitle( source.getXc() );

        return policeRankInfo;
    }

    @Override
    public PoliceRankInfo convert(VWjRyjxxx source, PoliceRankInfo target) {
        if ( source == null ) {
            return target;
        }

        target.setRankType( source.getSxzl() );
        target.setPromotionReason( source.getSxyy() );
        target.setIdCard( source.getGmsfhm() );
        target.setRankEndDate( ConversionUtils.strToDate( source.getXczzrq() ) );
        target.setPromotionDate( ConversionUtils.strToDate( source.getSxsj() ) );
        target.setRankStartDate( ConversionUtils.strToDate( source.getXcqsrq() ) );
        target.setAdminLevelAtPromotion( source.getSxsxzzj() );
        target.setRankTitle( source.getXc() );

        return target;
    }
}
