package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceDrinkReport;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-26T13:29:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjYjbbToPoliceDrinkReportMapperImpl implements VWjYjbbToPoliceDrinkReportMapper {

    @Override
    public PoliceDrinkReport convert(VWjYjbb source) {
        if ( source == null ) {
            return null;
        }

        PoliceDrinkReport policeDrinkReport = new PoliceDrinkReport();

        policeDrinkReport.setOrganizationId( source.getDwbm() );
        policeDrinkReport.setReason( source.getYjsy() );
        policeDrinkReport.setApproveResult( source.getShjg() );
        policeDrinkReport.setDrinkTime( ConversionUtils.strToDate( source.getYjrq() ) );
        policeDrinkReport.setIdCard( source.getGmsfhm() );
        policeDrinkReport.setName( source.getXm() );
        policeDrinkReport.setXxzj( source.getXxzjbh() );
        policeDrinkReport.setRemark( source.getBz() );
        policeDrinkReport.setInviter( source.getYyrxm() );
        policeDrinkReport.setLocation( source.getYjdd() );
        policeDrinkReport.setPayer( source.getFkrxm() );
        policeDrinkReport.setTravelMode( source.getCxfs() );
        policeDrinkReport.setParticipants( source.getCyrs() );

        return policeDrinkReport;
    }

    @Override
    public PoliceDrinkReport convert(VWjYjbb source, PoliceDrinkReport target) {
        if ( source == null ) {
            return target;
        }

        target.setOrganizationId( source.getDwbm() );
        target.setReason( source.getYjsy() );
        target.setApproveResult( source.getShjg() );
        target.setDrinkTime( ConversionUtils.strToDate( source.getYjrq() ) );
        target.setIdCard( source.getGmsfhm() );
        target.setName( source.getXm() );
        target.setXxzj( source.getXxzjbh() );
        target.setRemark( source.getBz() );
        target.setInviter( source.getYyrxm() );
        target.setLocation( source.getYjdd() );
        target.setPayer( source.getFkrxm() );
        target.setTravelMode( source.getCxfs() );
        target.setParticipants( source.getCyrs() );

        return target;
    }
}
