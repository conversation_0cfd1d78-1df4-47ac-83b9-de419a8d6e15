package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceMonthlyAssessment;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-26T10:29:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjRyydkhToPoliceMonthlyAssessmentMapperImpl implements VWjRyydkhToPoliceMonthlyAssessmentMapper {

    @Override
    public PoliceMonthlyAssessment convert(VWjRyydkh source) {
        if ( source == null ) {
            return null;
        }

        PoliceMonthlyAssessment policeMonthlyAssessment = new PoliceMonthlyAssessment();

        policeMonthlyAssessment.setAssessmentNotice( source.getKptbpzmc() );
        policeMonthlyAssessment.setAssessmentMonth( source.getYf() );
        policeMonthlyAssessment.setIdCard( source.getGmsfhm() );
        policeMonthlyAssessment.setAssessmentRanking( ConversionUtils.bigDecimalToString( source.getPm() ) );
        policeMonthlyAssessment.setAssessmentScore( ConversionUtils.bigDecimalToString( source.getDf() ) );
        policeMonthlyAssessment.setAssessmentBonus( ConversionUtils.bigDecimalToString( source.getJj() ) );
        policeMonthlyAssessment.setAssessmentYear( source.getNd() );

        return policeMonthlyAssessment;
    }

    @Override
    public PoliceMonthlyAssessment convert(VWjRyydkh source, PoliceMonthlyAssessment target) {
        if ( source == null ) {
            return target;
        }

        target.setAssessmentNotice( source.getKptbpzmc() );
        target.setAssessmentMonth( source.getYf() );
        target.setIdCard( source.getGmsfhm() );
        target.setAssessmentRanking( ConversionUtils.bigDecimalToString( source.getPm() ) );
        target.setAssessmentScore( ConversionUtils.bigDecimalToString( source.getDf() ) );
        target.setAssessmentBonus( ConversionUtils.bigDecimalToString( source.getJj() ) );
        target.setAssessmentYear( source.getNd() );

        return target;
    }
}
