package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceEducation;
import com.hl.archive.domain.entity.PoliceEducationToVWjRyxlxwMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__604;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__604.class,
    uses = {ConversionUtils.class,PoliceEducationToVWjRyxlxwMapper.class},
    imports = {}
)
public interface VWjRyxlxwToPoliceEducationMapper extends BaseMapper<VWjRyxlxw, PoliceEducation> {
  @Mapping(
      target = "graduationDate",
      source = "bysj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "educationLevel",
      source = "xl"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "enrollmentDate",
      source = "rxsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "majorName",
      source = "zymc"
  )
  @Mapping(
      target = "schoolName",
      source = "xxmc"
  )
  PoliceEducation convert(VWjRyxlxw source);

  @Mapping(
      target = "graduationDate",
      source = "bysj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "educationLevel",
      source = "xl"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "enrollmentDate",
      source = "rxsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "majorName",
      source = "zymc"
  )
  @Mapping(
      target = "schoolName",
      source = "xxmc"
  )
  PoliceEducation convert(VWjRyxlxw source, @MappingTarget PoliceEducation target);
}
