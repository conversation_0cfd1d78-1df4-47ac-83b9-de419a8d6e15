package com.hl.archive.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hl.archive.domain.dto.PoliceBaseQueryDTO;
import com.hl.archive.domain.dto.PoliceProjectReturnDTO;
import com.hl.archive.domain.entity.PoliceProjectContact;
import com.hl.archive.domain.entity.PoliceProjectEntryPerson;
import com.hl.archive.domain.entity.PoliceProjectMaterial;
import com.hl.archive.domain.entity.PoliceProjectStory;
import com.hl.archive.service.PoliceProjectContactService;
import com.hl.archive.service.PoliceProjectEntryPersonService;
import com.hl.archive.service.PoliceProjectMaterialService;
import com.hl.archive.service.PoliceProjectStoryService;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/project")
@Api(tags = "星火计划")
@RequiredArgsConstructor
@Slf4j
public class PoliceProjectController {

    private final PoliceProjectEntryPersonService policeProjectEntryPersonService;

    private final PoliceProjectContactService policeProjectContactService;

    private final PoliceProjectMaterialService policeProjectMaterialService;

    private final PoliceProjectStoryService policeProjectStoryService;


    @PostMapping("/queryProject")
    @ApiOperation(value = "查询星火计划")
    public R<PoliceProjectReturnDTO> queryProject(@RequestBody PoliceBaseQueryDTO request) {
        PoliceProjectReturnDTO policeProjectReturnDTO = new PoliceProjectReturnDTO();
        String idCard = request.getIdCard();

        // 第一步：查询入项个人信息（必须串行执行，因为后续查询依赖此结果）
        PoliceProjectEntryPerson one = policeProjectEntryPersonService.getOne(Wrappers.<PoliceProjectEntryPerson>lambdaQuery()
                .eq(PoliceProjectEntryPerson::getIdCard, idCard));
        if (one == null) {
            return R.ok(policeProjectReturnDTO);
        }
        policeProjectReturnDTO.setPoliceProjectEntryPerson(one);

        String zjbh = one.getZjbh();

        try {
            // 第二步：并发执行三个独立的查询操作
            CompletableFuture<List<PoliceProjectContact>> contactsFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return policeProjectContactService.list(Wrappers.<PoliceProjectContact>lambdaQuery()
                            .eq(PoliceProjectContact::getXhjhZjbh, zjbh));
                } catch (Exception e) {
                    log.error("查询培养联系信息失败, zjbh: {}", zjbh, e);
                    throw new RuntimeException("查询培养联系信息失败", e);
                }
            });

            CompletableFuture<List<PoliceProjectMaterial>> materialsFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return policeProjectMaterialService.list(Wrappers.<PoliceProjectMaterial>lambdaQuery()
                            .eq(PoliceProjectMaterial::getXhjhZjbh, zjbh));
                } catch (Exception e) {
                    log.error("查询图文资料信息失败, zjbh: {}", zjbh, e);
                    throw new RuntimeException("查询图文资料信息失败", e);
                }
            });

            CompletableFuture<List<PoliceProjectStory>> storiesFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return policeProjectStoryService.list(Wrappers.<PoliceProjectStory>lambdaQuery()
                            .eq(PoliceProjectStory::getXhjhZjbh, zjbh));
                } catch (Exception e) {
                    log.error("查询先进事迹信息失败, zjbh: {}", zjbh, e);
                    throw new RuntimeException("查询先进事迹信息失败", e);
                }
            });

            // 等待所有异步查询完成，设置超时时间为10秒
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(contactsFuture, materialsFuture, storiesFuture);
            allFutures.get(10, TimeUnit.SECONDS);

            // 获取查询结果并设置到返回对象中
            policeProjectReturnDTO.setPoliceProjectContacts(contactsFuture.get());
            policeProjectReturnDTO.setPoliceProjectMaterials(materialsFuture.get());
            policeProjectReturnDTO.setPoliceProjectStories(storiesFuture.get());

            log.info("星火计划查询完成, idCard: {}, zjbh: {}", idCard, zjbh);

        } catch (Exception e) {
            log.error("并发查询星火计划信息失败, idCard: {}, zjbh: {}", idCard, zjbh, e);
            // 如果并发查询失败，降级为串行查询
            log.warn("降级为串行查询, idCard: {}, zjbh: {}", idCard, zjbh);
            policeProjectReturnDTO.setPoliceProjectContacts(policeProjectContactService.list(Wrappers.<PoliceProjectContact>lambdaQuery()
                    .eq(PoliceProjectContact::getXhjhZjbh, zjbh)));
            policeProjectReturnDTO.setPoliceProjectMaterials(policeProjectMaterialService.list(Wrappers.<PoliceProjectMaterial>lambdaQuery()
                    .eq(PoliceProjectMaterial::getXhjhZjbh, zjbh)));
            policeProjectReturnDTO.setPoliceProjectStories(policeProjectStoryService.list(Wrappers.<PoliceProjectStory>lambdaQuery()
                    .eq(PoliceProjectStory::getXhjhZjbh, zjbh)));
        }

        return R.ok(policeProjectReturnDTO);
    }




}
