package com.hl.archive.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.constant.ProjectCommonConstants;
import com.hl.archive.domain.dto.ImportResultDTO;
import com.hl.archive.domain.dto.PoliceQualificationRecordAddDTO;
import com.hl.archive.domain.dto.PoliceQualificationRecordRequestDTO;
import com.hl.archive.domain.entity.PoliceQualificationRecord;
import com.hl.archive.factory.QualificationRecordStrategyFactory;
import com.hl.archive.service.PoliceQualificationRecordService;
import com.hl.archive.strategy.QualificationRecordStrategy;
import com.hl.archive.utils.SsoCacheUtil;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/qualificationRecord")
@RequiredArgsConstructor
@Slf4j
@Api(tags = "任职能力资格")
public class PoliceQualificationRecordController {

    private final PoliceQualificationRecordService policeQualificationRecordService;

    private final QualificationRecordStrategyFactory qualificationRecordStrategyFactory;

    @PostMapping("/add")
    @ApiOperation("新增任职能力资格")
    public R<?> add(@RequestBody PoliceQualificationRecordAddDTO record) {
        List<String> idCard = record.getIdCard();
        List<PoliceQualificationRecord> list = new ArrayList<>();
        for (String s : idCard) {
            PoliceQualificationRecord policeQualificationRecord = new PoliceQualificationRecord();
            policeQualificationRecord.setOrganizationId(SsoCacheUtil.getUserOrgIdByIdCard(s));
            policeQualificationRecord.setIdCard(s);
            policeQualificationRecord.setCategory(record.getCategory());
            policeQualificationRecord.setProject(record.getProject());
            policeQualificationRecord.setScore(record.getScore());
            policeQualificationRecord.setIssueDate(record.getIssueDate());
            policeQualificationRecord.setScoreQualified(record.getScoreQualified());
            policeQualificationRecord.setExpireDate(record.getExpireDate());
            policeQualificationRecord.setRemark(record.getRemark());
            if ("免测".equals(record.getScore())) {
                policeQualificationRecord.setScoreQualified("免测");
            }else if ("缓测".equals(record.getScore())) {
                policeQualificationRecord.setScoreQualified("缓测");
            }else {
                String project = record.getProject();
                QualificationRecordStrategy strategy = qualificationRecordStrategyFactory.getStrategy(project);
                policeQualificationRecord.setScoreQualified(strategy.isPass(strategy.calculatePoint(s, record.getScore())) ? "合格" : "不合格");
            }
            list.add(policeQualificationRecord);
        }
        boolean save = policeQualificationRecordService.saveBatch(list);
        return R.ok(save);
    }

    @PostMapping("/page")
    @ApiOperation("分页查询")
    public R<List<PoliceQualificationRecord>> page(@RequestBody PoliceQualificationRecordRequestDTO requestDTO) {
        LambdaQueryWrapper<PoliceQualificationRecord> queryWrapper = Wrappers.<PoliceQualificationRecord>lambdaQuery()
                .eq(StrUtil.isNotBlank(requestDTO.getIdCard()), PoliceQualificationRecord::getIdCard, requestDTO.getIdCard())
                .eq(StrUtil.isNotBlank(requestDTO.getCategory()), PoliceQualificationRecord::getCategory, requestDTO.getCategory())
                .eq(StrUtil.isNotBlank(requestDTO.getProject()), PoliceQualificationRecord::getProject, requestDTO.getProject());
        if (StrUtil.isNotBlank(requestDTO.getOrganizationId())) {
            if (!ProjectCommonConstants.WU_JIN_ORG_CODE.equals(requestDTO.getOrganizationId())) {
                queryWrapper.likeRight(PoliceQualificationRecord::getOrganizationId, requestDTO.getOrganizationId().substring(0, 8));
            }
        }
        if (StrUtil.isNotBlank(requestDTO.getScoreQualified())){
            queryWrapper.eq(PoliceQualificationRecord::getScoreQualified, requestDTO.getScoreQualified());
        }
        if (requestDTO.getAllQualified()){
            // 查询体能和考试都合格的人员
            List<String> qualifiedIdCards = policeQualificationRecordService.getQualifiedIdCards(requestDTO);
            if (qualifiedIdCards.isEmpty()) {
                // 如果没有符合条件的人员，返回空结果
                return R.ok(new ArrayList<>(), 0);
            }
            queryWrapper.in(PoliceQualificationRecord::getIdCard, qualifiedIdCards);
        }

        Page<PoliceQualificationRecord> page = policeQualificationRecordService.page(Page.of(requestDTO.getPage(), requestDTO.getLimit()), queryWrapper);

        return R.ok(page.getRecords(), (int) page.getTotal());
    }




    @PostMapping("/update")
    @ApiOperation("更新任职能力资格")
    public R<?> update(@RequestBody PoliceQualificationRecord record) {
        boolean update = policeQualificationRecordService.updateById(record);
        return R.ok(update);
    }

    @PostMapping("/delete")
    @ApiOperation("删除任职能力资格")
    public R<?> delete(@RequestBody PoliceQualificationRecord record) {
        boolean remove = policeQualificationRecordService.removeById(record);
        return R.ok(remove);
    }

    @GetMapping("/downloadTemplate")
    @ApiOperation("下载模板")
    public void downloadTemplate(HttpServletResponse response,
                                 @RequestParam("category") String category) throws IOException {
        // 根据category 下载conf下不同的模板文件

        // 定义category与模板文件的映射关系
        Map<String, String> templateMap = new HashMap<>();
        templateMap.put("体能", "conf/template/体能导入模板.xlsx");
        templateMap.put("考试", "conf/template/考试导入模板.xlsx");

        // 获取对应的模板文件路径
        String templatePath = templateMap.get(category);
        if (templatePath == null) {
            log.error("不支持的category类型: {}", category);
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "不支持的category类型: " + category);
            return;
        }

        // 检查文件是否存在
        Path filePath = Paths.get(templatePath);
        if (!Files.exists(filePath)) {
            log.error("模板文件不存在: {}", templatePath);
            response.sendError(HttpServletResponse.SC_NOT_FOUND, "模板文件不存在");
            return;
        }

        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");

            // 设置文件名
            String fileName = URLEncoder.encode(category + "导入模板", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");

            // 读取文件并写入响应流
            try (InputStream inputStream = Files.newInputStream(filePath);
                 OutputStream outputStream = response.getOutputStream();
                 BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(outputStream)) {

                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    bufferedOutputStream.write(buffer, 0, bytesRead);
                }
                bufferedOutputStream.flush();
            }

            log.info("成功下载模板文件: category={}, file={}", category, templatePath);

        } catch (IOException e) {
            log.error("下载模板文件失败: category={}, file={}", category, templatePath, e);
            throw e;
        }
    }

    @PostMapping("/importData")
    @ApiOperation("导入数据")
    public R<ImportResultDTO> importData(@RequestParam("file") MultipartFile file,
                                         @RequestParam("category") String category) {
        try {
            // 验证文件
            if (file == null || file.isEmpty()) {
                return R.fail("请选择要导入的文件");
            }

            // 验证文件类型
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || !originalFilename.toLowerCase().endsWith(".xlsx")) {
                return R.fail("请上传Excel文件(.xlsx格式)");
            }

            // 验证category
            if (StrUtil.isBlank(category)) {
                return R.fail("资格类目不能为空");
            }

            // 验证category是否支持
            if (!"体能".equals(category) && !"考试".equals(category)) {
                return R.fail("不支持的资格类目: " + category);
            }

            // 验证文件大小（限制10MB）
            if (file.getSize() > 10 * 1024 * 1024) {
                return R.fail("文件大小不能超过10MB");
            }

            log.info("开始导入数据: category={}, filename={}, size={}", category, originalFilename, file.getSize());

            // 执行导入
            ImportResultDTO result = policeQualificationRecordService.importData(file, category);

            if (result.getSuccess()) {
                log.info("导入完成: 总数={}, 成功={}, 失败={}",
                        result.getTotalCount(), result.getSuccessCount(), result.getFailCount());
                return R.ok();
            } else {
                log.warn("导入部分失败: 总数={}, 成功={}, 失败={}",
                        result.getTotalCount(), result.getSuccessCount(), result.getFailCount());
                return R.ok();
            }

        } catch (Exception e) {
            log.error("导入数据失败: category={}, filename={}", category, file.getOriginalFilename(), e);
            return R.fail("导入失败: " + e.getMessage());
        }
    }


    @PostMapping("/queryLatestRecord")
    @ApiOperation("查询最新记录")
    public R<List<PoliceQualificationRecord>> queryLatestRecord(@RequestBody PoliceQualificationRecordRequestDTO requestDTO) {
        List<PoliceQualificationRecord> list = policeQualificationRecordService.queryLatestRecord(requestDTO);
        return R.ok(list);
    }


    @GetMapping("/importPhysical")
    @ApiOperation("导入体能数据")
    public R<?> importPhysical(@RequestParam("fileId") String fileId) {
        policeQualificationRecordService.importPhysical(fileId);
        return R.ok();
    }

    @GetMapping("/importPhysicalIgnore")
    @ApiOperation("导入体能数据")
    public R<?> importPhysicalIgnore(@RequestParam("fileId") String fileId) {
        policeQualificationRecordService.importPhysicalIgnore(fileId);
        return R.ok();
    }

}