package com.hl.archive.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PolicePersonWarnRecordRequestDTO;
import com.hl.archive.domain.dto.PolicePersonWarnRecordTxTaskCreateDTO;
import com.hl.archive.domain.dto.PolicePersonWarnRecordTxTaskQueryDTO;
import com.hl.archive.domain.entity.PolicePersonWarnRecord;
import com.hl.archive.service.PolicePersonWarnRecordService;
import com.hl.common.domain.R;
import com.hl.security.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/policePersonWarnRecord")
@Slf4j
@RequiredArgsConstructor
@Api(tags = "民警预警记录")
public class PolicePersonWarnRecordController {

    private final PolicePersonWarnRecordService policePersonWarnRecordService;

    @PostMapping("/page")
    @ApiOperation("分页查询")
    public R<List<PolicePersonWarnRecord>> page(@RequestBody PolicePersonWarnRecordRequestDTO requestDTO) {
        Page<PolicePersonWarnRecord> page = policePersonWarnRecordService.pageList(requestDTO);
        return R.ok(page.getRecords(), (int) page.getTotal());
    }


    @PostMapping("/update")
    @ApiOperation("更新预警记录 用来签收  和反馈 更新对应字段")
    public R<Boolean> update(@RequestBody PolicePersonWarnRecord record) {
        record.setUpdateBy(UserUtils.getUser().getIdCard());
        record.setUpdateAt(LocalDateTime.now());
        return R.ok(policePersonWarnRecordService.updateById(record));
    }


    @PostMapping("/exportWarnInfo")
    @ApiOperation("导出预警信息")
    public void exportWarnInfo(@RequestBody PolicePersonWarnRecordRequestDTO requestDTO, HttpServletResponse response) throws IOException {
        policePersonWarnRecordService.exportWarnInfo(requestDTO,response);
    }



    @PostMapping("/createTxTask")
    @ApiOperation("创建谈心谈话任务")
    public R<?> createTxTask(@RequestBody PolicePersonWarnRecordTxTaskCreateDTO requestDTO) {
        JSONArray txTask = policePersonWarnRecordService.createTxTask(requestDTO);
        return R.ok(txTask);
    }

    @PostMapping("/listTaskInfo")
    @ApiOperation("更具数据id列出指定任务信息")
    public R<?> listTaskInfo(@RequestBody PolicePersonWarnRecordTxTaskQueryDTO requestDTO) {
        return policePersonWarnRecordService.listTaskInfo(requestDTO);
    }

}
