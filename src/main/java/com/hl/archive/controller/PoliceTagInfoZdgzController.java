package com.hl.archive.controller;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PoliceTagInfoZdgzAddDTO;
import com.hl.archive.domain.dto.PoliceTagInfoZdgzQueryDTO;
import com.hl.archive.domain.dto.PoliceTagInfoZdgzReturnDTO;

import com.hl.archive.service.PoliceTagInfoZdgzService;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/tagInfoZdgz")
@Api(tags = "重点关注")
@RequiredArgsConstructor
public class PoliceTagInfoZdgzController {


    private final PoliceTagInfoZdgzService policeTagInfoZdgzService;


    @PostMapping("/addTag")
    @ApiOperation("添加重点关注")
    public R<?> addTag(@RequestBody PoliceTagInfoZdgzAddDTO dto) {
        boolean b = policeTagInfoZdgzService.addTag(dto);
        return R.ok(b);
    }


    @PostMapping("/listTag")
    @ApiOperation("列出重点关注")
    public R<List<PoliceTagInfoZdgzReturnDTO>> listTag(@RequestBody PoliceTagInfoZdgzQueryDTO  dto) {
        Page<PoliceTagInfoZdgzReturnDTO> page = policeTagInfoZdgzService.listTag(dto);
        return R.ok(page.getRecords(),(int)page.getTotal());
    }


    @PostMapping("/batchDelete")
    @ApiOperation("批量删除")
    public R<?> batchDelete(@RequestBody JSONObject param) {
        boolean b = policeTagInfoZdgzService.removeBatchByIds(param.getList("ids", String.class));
        return R.ok(b);
    }

}
