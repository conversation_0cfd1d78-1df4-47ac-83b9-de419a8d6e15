package com.hl.archive.controller;

import com.hl.archive.domain.dto.UserBasicInfoDTO;
import com.hl.archive.service.UserCacheService;

import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 用户缓存控制器
 */
@Api(tags = "用户缓存管理")
@RestController
@RequestMapping("/userCache")
@RequiredArgsConstructor
public class UserCacheController {

    private final UserCacheService userCacheService;

    @GetMapping("/by-id-card/{idCard}")
    @ApiOperation("根据身份证号获取用户信息")
    public R<UserBasicInfoDTO> getUserByIdCard(
            @ApiParam(value = "身份证号", required = true) @PathVariable String idCard) {
        UserBasicInfoDTO userInfo = userCacheService.getUserByIdCard(idCard);
        if (userInfo != null) {
            return R.ok(userInfo);
        } else {
            return R.fail("未找到该身份证号对应的用户信息");
        }
    }

    @GetMapping("/by-police-number/{policeNumber}")
    @ApiOperation("根据警号获取用户信息")
    public R<UserBasicInfoDTO> getUserByPoliceNumber(
            @ApiParam(value = "警号", required = true) @PathVariable String policeNumber) {
        UserBasicInfoDTO userInfo = userCacheService.getUserByPoliceNumber(policeNumber);
        if (userInfo != null) {
            return R.ok(userInfo);
        } else {
            return R.fail("未找到该警号对应的用户信息");
        }
    }

    @PostMapping("/refresh")
    @ApiOperation("手动刷新所有用户缓存")
    public R<String> refreshCache() {
        try {
            userCacheService.refreshAllUserCache();
            return R.ok("缓存刷新成功");
        } catch (Exception e) {
            return R.fail("缓存刷新失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/evict")
    @ApiOperation("清除指定用户缓存")
    public R<String> evictUserCache(
            @ApiParam("身份证号") @RequestParam(required = false) String idCard,
            @ApiParam("警号") @RequestParam(required = false) String policeNumber) {
        try {
            userCacheService.evictUserCache(idCard, policeNumber);
            return R.ok("缓存清除成功");
        } catch (Exception e) {
            return R.fail("缓存清除失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/evict-all")
    @ApiOperation("清除所有用户缓存")
    public R<String> evictAllCache() {
        try {
            userCacheService.evictAllUserCache();
            return R.ok("所有缓存清除成功");
        } catch (Exception e) {
            return R.fail("缓存清除失败: " + e.getMessage());
        }
    }

    @GetMapping("/stats")
    @ApiOperation("获取缓存统计信息")
    public R<String> getCacheStats() {
        try {
            String stats = userCacheService.getCacheStats();
            return R.ok(stats);
        } catch (Exception e) {
            return R.fail("获取缓存统计失败: " + e.getMessage());
        }
    }




}
