package com.hl.archive.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PoliceNewProfileQueryDTO;
import com.hl.archive.domain.dto.PoliceNewProfileRoleReturnDTO;
import com.hl.archive.domain.dto.SimpleTimelineEventDTO;
import com.hl.archive.domain.dto.SimpleTimelineQueryDTO;
import com.hl.archive.domain.entity.PoliceNewProfile;
import com.hl.archive.service.PoliceNewProfileService;
import com.hl.common.domain.R;
import com.hl.security.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/policeNewProfile")
@Api(tags = "新警画像")
@RequiredArgsConstructor
@Slf4j
public class PoliceNewProfileController {

    private final PoliceNewProfileService policeNewProfileService;


    @GetMapping("/checkRole")
    @ApiOperation("获取权限")
    public R<PoliceNewProfileRoleReturnDTO> checkRole() {
        PoliceNewProfileRoleReturnDTO returnDTO = policeNewProfileService.checkRole();
        return R.ok(returnDTO);
    }


    @PostMapping("/page")
    @ApiOperation("分页查询")
    public R<List<PoliceNewProfile>> page(@RequestBody PoliceNewProfileQueryDTO requestDTO) {
        Page<PoliceNewProfile> page = policeNewProfileService.pageList(requestDTO);
        return R.ok(page.getRecords(), (int) page.getTotal());

    }

    @PostMapping("/add")
    @ApiOperation("添加")
    public R<Boolean> add(@RequestBody PoliceNewProfile request) {
        request.setCreatedBy(UserUtils.getUser().getIdCard());
        return R.ok(policeNewProfileService.save(request));
    }

    @PostMapping("/update")
    @ApiOperation("更新")
    public R<Boolean> update(@RequestBody PoliceNewProfile request) {
        request.setUpdatedBy(UserUtils.getUser().getIdCard());
        request.setUpdatedAt(LocalDateTime.now());
        return R.ok(policeNewProfileService.updateById(request));
    }

    @PostMapping("/delete")
    @ApiOperation("删除")
    public R<Boolean> delete(@RequestBody PoliceNewProfile request) {
        return R.ok(policeNewProfileService.removeById(request));
    }

    @PostMapping("/exportListWord")
    @ApiOperation("导出Excel")
    public void export(@RequestBody PoliceNewProfileQueryDTO requestDTO, HttpServletResponse response) throws IOException {
        policeNewProfileService.exportPoliceListWord(requestDTO, response);
    }


    @PostMapping("/exportOneWord")
    @ApiOperation("导出Word")
    public void exportWord(@RequestBody PoliceNewProfileQueryDTO requestDTO, HttpServletResponse response) throws IOException {
        policeNewProfileService.exportPoliceNewProfileWord(requestDTO, response);
    }

    @PostMapping("/timeline")
    @ApiOperation("获取新警事件时间轴")
    public R<List<SimpleTimelineEventDTO>> getTimeline(@RequestBody SimpleTimelineQueryDTO requestDTO) {
        List<SimpleTimelineEventDTO> events = policeNewProfileService.getTimelineEvents(requestDTO);
        return R.ok(events);
    }





}
