package com.hl.archive.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PoliceTagInfoZdgzQueryDTO;
import com.hl.archive.domain.dto.PoliceTagInfoZdgzReturnDTO;
import com.hl.archive.domain.entity.PoliceTagInfoZdgz;
import org.apache.ibatis.annotations.Param;

public interface PoliceTagInfoZdgzMapper extends BaseMapper<PoliceTagInfoZdgz> {
    Page<PoliceTagInfoZdgzReturnDTO> listTag(@Param("page") Page<PoliceTagInfoZdgzReturnDTO> page, @Param("request") PoliceTagInfoZdgzQueryDTO dto);
}