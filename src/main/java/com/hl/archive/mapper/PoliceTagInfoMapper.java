package com.hl.archive.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PolicePersonalTagQueryDTO;
import com.hl.archive.domain.dto.PolicePersonalTagReturnDTO;
import com.hl.archive.domain.dto.TagDrillDownDTO;
import com.hl.archive.domain.dto.TagStatisticsQueryDTO;
import com.hl.archive.domain.entity.PoliceBasicInfo;
import com.hl.archive.domain.entity.PoliceTagInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PoliceTagInfoMapper extends BaseMapper<PoliceTagInfo> {

    /**
     * 根据标签类型获取标签信息列表（简单查询）
     */
    List<PoliceTagInfo> getTagInfoByType(TagStatisticsQueryDTO request);

    /**
     * 根据标签类型获取标签信息列表（用于穿透查询）
     */
    List<PoliceTagInfo> getTagInfoForDrillDown(TagDrillDownDTO request);

    /**
     * 根据身份证号列表获取人员基本信息（分页）
     */
    Page<PoliceBasicInfo> getPoliceByIdCards(@Param("page") Page<PoliceBasicInfo> page, @Param("idCards") List<String> idCards, @Param("organizationId") String organizationId);

    Page<PolicePersonalTagReturnDTO> pagePolicePersonalTag(@Param("page") Page<PolicePersonalTagReturnDTO> page,
                                                           @Param("param") PolicePersonalTagQueryDTO param);
}