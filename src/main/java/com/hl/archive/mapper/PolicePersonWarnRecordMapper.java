package com.hl.archive.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hl.archive.domain.dto.WarnTypeStatisticsDTO;
import com.hl.archive.domain.entity.PolicePersonWarnRecord;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

public interface PolicePersonWarnRecordMapper extends BaseMapper<PolicePersonWarnRecord> {

    /**
     * 统计不同预警类别的数量
     * @param organizationId 组织机构ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 预警类别统计结果
     */
    List<WarnTypeStatisticsDTO> getWarnTypeStatistics(@Param("organizationId") String organizationId,
                                                      @Param("startTime") LocalDateTime startTime,
                                                      @Param("endTime") LocalDateTime endTime);
}