package com.hl.archive.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hl.archive.domain.dto.PoliceQualificationRecordRequestDTO;
import com.hl.archive.domain.entity.PoliceQualificationRecord;

import java.util.List;

public interface PoliceQualificationRecordMapper extends BaseMapper<PoliceQualificationRecord> {

    List<PoliceQualificationRecord> queryLatestRecord(PoliceQualificationRecordRequestDTO requestDTO);

    /**
     * 获取体能和考试都合格的人员身份证号列表
     * @param requestDTO 查询条件
     * @return 符合条件的身份证号列表
     */
    List<String> getQualifiedIdCards(PoliceQualificationRecordRequestDTO requestDTO);
}