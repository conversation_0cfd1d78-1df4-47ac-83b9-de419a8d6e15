package com.hl.archive.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.NewPoliceRankingDTO;
import com.hl.archive.domain.dto.NewPoliceRankingRequestDTO;
import com.hl.archive.domain.entity.PoliceNewProfile;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

public interface PoliceNewProfileMapper extends BaseMapper<PoliceNewProfile> {

    /**
     * 获取新警接处警数量排名
     * @param page 分页参数
     * @param organizationId 组织机构ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 新警接处警排名结果
     */
    Page<NewPoliceRankingDTO> getNewPoliceCallRanking(@Param("page") Page<NewPoliceRankingDTO> page,
                                                      @Param("organizationId") String organizationId,
                                                      @Param("startTime") LocalDateTime startTime,
                                                      @Param("endTime") LocalDateTime endTime);

    /**
     * 获取新警办案数量排名
     * @param page 分页参数
     * @param organizationId 组织机构ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 新警办案排名结果
     */
    Page<NewPoliceRankingDTO> getNewPoliceCaseRanking(@Param("page") Page<NewPoliceRankingDTO> page,
                                                      @Param("organizationId") String organizationId,
                                                      @Param("startTime") LocalDateTime startTime,
                                                      @Param("endTime") LocalDateTime endTime);
}