package com.hl.archive.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.NewPoliceRankingDTO;
import com.hl.archive.domain.dto.NewPoliceRankingRequestDTO;
import com.hl.archive.domain.entity.PoliceNewProfile;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

public interface PoliceNewProfileMapper extends BaseMapper<PoliceNewProfile> {

    // 排名查询逻辑已移至Service层处理，不再需要复杂的SQL查询
}