package com.hl.archive.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.hl.archive.config.UserCacheConfig;
import com.hl.archive.domain.dto.UserBasicInfoDTO;
import com.hl.archive.domain.dto.UserSearchQueryDTO;
import com.hl.archive.domain.entity.AuxiliaryPoliceInfo;
import com.hl.archive.domain.entity.PoliceBasicInfo;
import com.hl.archive.utils.SsoCacheUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.Period;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 用户缓存服务 - 汇聚民警和辅警信息，提供基于身份证和警号的查询
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UserCacheService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final UserCacheConfig cacheConfig;
    private final PoliceBasicInfoService policeBasicInfoService;
    private final AuxiliaryPoliceInfoService auxiliaryPoliceInfoService;

    /**
     * 应用启动后初始化缓存
     */
    @EventListener(ApplicationReadyEvent.class)
    public void initCache() {
        if (cacheConfig.isEnabled()) {
            log.info("开始初始化用户缓存...");
            refreshAllUserCache();
            log.info("用户缓存初始化完成");
        }
    }

    /**
     * 定时刷新缓存
     */
    @Scheduled(fixedDelayString = "#{@userCacheConfig.scheduledRefreshInterval * 1000}")
    public void scheduledRefreshCache() {
        if (cacheConfig.isEnabled() && cacheConfig.isEnableScheduledRefresh()) {
            log.info("开始定时刷新用户缓存...");
            refreshAllUserCache();
            log.info("定时刷新用户缓存完成");
        }
    }

    /**
     * 刷新所有用户缓存
     */
    public void refreshAllUserCache() {
        try {
            // 刷新民警缓存
            refreshPoliceCache();
            // 刷新辅警缓存
            refreshAuxiliaryPoliceCache();
        } catch (Exception e) {
            log.error("刷新用户缓存失败", e);
        }
    }

    /**
     * 单独刷新民警缓存
     */
    public void refreshPoliceCacheOnly() {
        try {
            refreshPoliceCache();
        } catch (Exception e) {
            log.error("刷新民警缓存失败", e);
        }
    }

    /**
     * 单独刷新辅警缓存
     */
    public void refreshAuxiliaryCacheOnly() {
        try {
            refreshAuxiliaryPoliceCache();
        } catch (Exception e) {
            log.error("刷新辅警缓存失败", e);
        }
    }

    /**
     * 刷新民警缓存
     */
    private void refreshPoliceCache() {
        log.info("开始刷新民警缓存...");
        List<PoliceBasicInfo> policeList = policeBasicInfoService.list();

        int count = 0;
        for (PoliceBasicInfo police : policeList) {
            UserBasicInfoDTO userInfo = convertPoliceToUserInfo(police);
            if (userInfo != null) {
                cacheUserInfo(userInfo);
                count++;
            }
        }

        log.info("民警缓存刷新完成，共缓存 {} 条记录", count);
    }

    /**
     * 刷新辅警缓存
     */
    private void refreshAuxiliaryPoliceCache() {
        log.info("开始刷新辅警缓存...");
        List<AuxiliaryPoliceInfo> auxiliaryList = auxiliaryPoliceInfoService.list();

        int count = 0;
        for (AuxiliaryPoliceInfo auxiliary : auxiliaryList) {
            UserBasicInfoDTO userInfo = convertAuxiliaryToUserInfo(auxiliary);
            if (userInfo != null) {
                cacheUserInfo(userInfo);
                count++;
            }
        }

        log.info("辅警缓存刷新完成，共缓存 {} 条记录", count);
    }

    /**
     * 将民警信息转换为用户基本信息
     */
    private UserBasicInfoDTO convertPoliceToUserInfo(PoliceBasicInfo police) {
        if (police == null || StrUtil.isBlank(police.getIdCard())) {
            return null;
        }

        UserBasicInfoDTO userInfo = new UserBasicInfoDTO();
        userInfo.setIdCard(police.getIdCard());
        userInfo.setPoliceNumber(police.getPoliceNumber());
        userInfo.setName(police.getName());

        // 性别转换：1-男，2-女
        if (police.getGender() != null) {
            userInfo.setGender(police.getGender() == 1 ? "男" : "女");
        }
        userInfo.setBirthDate(police.getBirthDate());
        userInfo.setUnitName(police.getUnitName());
        userInfo.setDepartment(police.getDepartment());
        userInfo.setPosition(police.getPositionName());
        userInfo.setEducationLevel(police.getEducationLevel());
        userInfo.setPoliticalStatus(police.getPoliticalIdentity());
        userInfo.setMaritalStatus(police.getMarriageStatus());
        userInfo.setPhoneNumber(police.getMobilePhone());
        userInfo.setBloodType(police.getBloodType());
        userInfo.setNation(police.getNation());
        userInfo.setNativePlace(police.getNativePlace());
        userInfo.setUserType(UserBasicInfoDTO.UserType.POLICE.getCode());
        userInfo.setOrganizationId(police.getOrganizationId());
        userInfo.setDutyStatus(police.getDutyStatus());
        userInfo.setImgUrl(police.getImgUrl());
        userInfo.setOrganizationName(SsoCacheUtil.getOrganizationName(police.getOrganizationId()));

        return userInfo;
    }

    /**
     * 将辅警信息转换为用户基本信息
     */
    private UserBasicInfoDTO convertAuxiliaryToUserInfo(AuxiliaryPoliceInfo auxiliary) {
        if (auxiliary == null || StrUtil.isBlank(auxiliary.getIdCard())) {
            return null;
        }

        UserBasicInfoDTO userInfo = new UserBasicInfoDTO();
        userInfo.setIdCard(auxiliary.getIdCard());
        userInfo.setEmployeeNumber(auxiliary.getEmployeeNumber());
        userInfo.setName(auxiliary.getName());
        userInfo.setGender(auxiliary.getGender());
        userInfo.setBirthDate(auxiliary.getBirthDate());
        userInfo.setUnitName(auxiliary.getOrganization());
        userInfo.setPosition(auxiliary.getPosition());
        userInfo.setEducationLevel(auxiliary.getEducationLevel());
        userInfo.setPoliticalStatus(auxiliary.getPoliticalStatus());
        userInfo.setMaritalStatus(auxiliary.getMaritalStatus());
        userInfo.setPhoneNumber(auxiliary.getPhoneNumber());
        userInfo.setBloodType(auxiliary.getBloodType());
        userInfo.setNation(auxiliary.getEthnicity());
        userInfo.setNativePlace(auxiliary.getNativePlace());
        userInfo.setUserType(UserBasicInfoDTO.UserType.AUXILIARY.getCode());
        userInfo.setOrganizationId(auxiliary.getOrganizationId());
        userInfo.setDutyStatus(auxiliary.getEmploymentStatus());
        userInfo.setOrganizationName(SsoCacheUtil.getOrganizationName(auxiliary.getOrganizationId()));

        return userInfo;
    }

    /**
     * 缓存用户信息
     */
    private void cacheUserInfo(UserBasicInfoDTO userInfo) {
        if (userInfo == null) {
            return;
        }

        try {
            // 以身份证为key缓存
            if (StrUtil.isNotBlank(userInfo.getIdCard())) {
                String idCardKey = buildIdCardKey(userInfo.getIdCard());
                redisTemplate.opsForValue().set(idCardKey, userInfo, cacheConfig.getExpireTime(), TimeUnit.SECONDS);
            }

            // 以警号为key缓存（仅民警）
            if (StrUtil.isNotBlank(userInfo.getPoliceNumber())) {
                String policeKey = buildPoliceNumberKey(userInfo.getPoliceNumber());
                redisTemplate.opsForValue().set(policeKey, userInfo, cacheConfig.getExpireTime(), TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            log.error("缓存用户信息失败: {}", userInfo.getIdCard(), e);
        }
    }

    /**
     * 根据身份证号获取用户信息
     */
    public UserBasicInfoDTO getUserByIdCard(String idCard) {
        if (StrUtil.isBlank(idCard)) {
            return null;
        }

        try {
            String key = buildIdCardKey(idCard);
            Object cached = redisTemplate.opsForValue().get(key);

            if (cached instanceof UserBasicInfoDTO) {
                return (UserBasicInfoDTO) cached;
            }

            // 缓存未命中，从数据库查询并缓存
            return loadAndCacheUserByIdCard(idCard);
        } catch (Exception e) {
            log.error("根据身份证号获取用户信息失败: {}", idCard, e);
            return loadAndCacheUserByIdCard(idCard);
        }
    }

    /**
     * 根据警号获取用户信息
     */
    public UserBasicInfoDTO getUserByPoliceNumber(String policeNumber) {
        if (StrUtil.isBlank(policeNumber)) {
            return null;
        }

        try {
            String key = buildPoliceNumberKey(policeNumber);
            Object cached = redisTemplate.opsForValue().get(key);

            if (cached instanceof UserBasicInfoDTO) {
                return (UserBasicInfoDTO) cached;
            }

            // 缓存未命中，从数据库查询并缓存
            return loadAndCacheUserByPoliceNumber(policeNumber);
        } catch (Exception e) {
            log.error("根据警号获取用户信息失败: {}", policeNumber, e);
            return loadAndCacheUserByPoliceNumber(policeNumber);
        }
    }

    /**
     * 从数据库加载用户信息并缓存（根据身份证）
     */
    private UserBasicInfoDTO loadAndCacheUserByIdCard(String idCard) {
        UserBasicInfoDTO userInfo = null;

        // 先查民警表
        PoliceBasicInfo police = policeBasicInfoService.getByIdCard(idCard);
        if (police != null) {
            userInfo = convertPoliceToUserInfo(police);
        } else {
            // 再查辅警表
            AuxiliaryPoliceInfo auxiliary = auxiliaryPoliceInfoService.getByIdCard(idCard);
            if (auxiliary != null) {
                userInfo = convertAuxiliaryToUserInfo(auxiliary);
            }
        }

        // 缓存结果（即使为null也缓存，避免频繁查询数据库）
        if (userInfo != null) {
            cacheUserInfo(userInfo);
        } else {
            // 缓存空值，设置较短的过期时间
            String key = buildIdCardKey(idCard);
            redisTemplate.opsForValue().set(key, "NULL", 300, TimeUnit.SECONDS);
        }

        return userInfo;
    }

    /**
     * 从数据库加载用户信息并缓存（根据警号）
     */
    private UserBasicInfoDTO loadAndCacheUserByPoliceNumber(String policeNumber) {
        UserBasicInfoDTO userInfo = null;

        // 只查民警表（辅警没有警号）
        PoliceBasicInfo police = policeBasicInfoService.getOne(
            policeBasicInfoService.lambdaQuery().eq(PoliceBasicInfo::getPoliceNumber, policeNumber).last("LIMIT 1")
        );

        if (police != null) {
            userInfo = convertPoliceToUserInfo(police);
            cacheUserInfo(userInfo);
        } else {
            // 缓存空值
            String key = buildPoliceNumberKey(policeNumber);
            redisTemplate.opsForValue().set(key, "NULL", 300, TimeUnit.SECONDS);
        }

        return userInfo;
    }

    /**
     * 构建身份证缓存key
     */
    private String buildIdCardKey(String idCard) {
        return cacheConfig.getKeyPrefix() + cacheConfig.getIdCardKey() + idCard;
    }

    /**
     * 构建警号缓存key
     */
    private String buildPoliceNumberKey(String policeNumber) {
        return cacheConfig.getKeyPrefix() + cacheConfig.getPoliceNumberKey() + policeNumber;
    }

    /**
     * 清除指定用户的缓存
     */
    public void evictUserCache(String idCard, String policeNumber) {
        try {
            if (StrUtil.isNotBlank(idCard)) {
                redisTemplate.delete(buildIdCardKey(idCard));
            }
            if (StrUtil.isNotBlank(policeNumber)) {
                redisTemplate.delete(buildPoliceNumberKey(policeNumber));
            }
        } catch (Exception e) {
            log.error("清除用户缓存失败: idCard={}, policeNumber={}", idCard, policeNumber, e);
        }
    }

    /**
     * 清除所有用户缓存
     */
    public void evictAllUserCache() {
        try {
            String pattern = cacheConfig.getKeyPrefix() + "*";
            redisTemplate.delete(redisTemplate.keys(pattern));
            log.info("已清除所有用户缓存");
        } catch (Exception e) {
            log.error("清除所有用户缓存失败", e);
        }
    }

    /**
     * 获取缓存统计信息
     */
    public String getCacheStats() {
        try {
            String idCardPattern = cacheConfig.getKeyPrefix() + cacheConfig.getIdCardKey() + "*";
            String policePattern = cacheConfig.getKeyPrefix() + cacheConfig.getPoliceNumberKey() + "*";

            long idCardCount = redisTemplate.keys(idCardPattern).size();
            long policeCount = redisTemplate.keys(policePattern).size();

            return String.format("用户缓存统计 - 身份证缓存: %d, 警号缓存: %d", idCardCount, policeCount);
        } catch (Exception e) {
            log.error("获取缓存统计信息失败", e);
            return "获取缓存统计信息失败";
        }
    }










}
