package com.hl.archive.service;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.constant.ProjectCommonConstants;
import com.hl.archive.domain.dto.*;
import com.hl.archive.domain.entity.*;
import com.hl.archive.enums.TagTypeEnum;
import com.hl.archive.mapper.PoliceTagInfoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 民警标签信息服务
 * 处理四种标签类型：登封训练营、实战能力体系、重点关注、表彰奖励
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PoliceTagInfoService extends ServiceImpl<PoliceTagInfoMapper, PoliceTagInfo> {

    private final UserCacheService userCacheService;

    private final PoliceTagInfoMapper policeTagInfoMapper;

    private final PoliceProjectEntryPersonService policeProjectEntryPersonService;

    private final PoliceTagInfoXjdfService policeTagInfoXjdfService;

    private final PoliceTagInfoZdgzService policeTagInfoZdgzService;

    private final PoliceHonorsService policeHonorsService;

    /**
     * 获取标签统计信息
     */
    public com.hl.archive.domain.dto.TagStatisticsDTO getTagStatistics(TagStatisticsQueryDTO request) {
        // 验证标签类型
        TagTypeEnum tagTypeEnum = TagTypeEnum.getByCode(request.getTagType());
        if (tagTypeEnum == null) {
            throw new IllegalArgumentException("无效的标签类型: " + request.getTagType());
        }

//        // 获取标签信息列表
//        List<PoliceTagInfo> tagInfoList = policeTagInfoMapper.getTagInfoByType(request);
        LambdaQueryWrapper<PoliceTagInfoXjdf> queryWrapper = Wrappers.<PoliceTagInfoXjdf>lambdaQuery()
                .eq(PoliceTagInfoXjdf::getTagType, request.getTagType());
        if (StrUtil.isNotBlank(request.getOrganizationId())) {
            if (ProjectCommonConstants.WU_JIN_ORG_CODE.equals(request.getOrganizationId())) {
                queryWrapper.eq(PoliceTagInfoXjdf::getOrganizationId, ProjectCommonConstants.WU_JIN_ORG_CODE);
            }else {
                queryWrapper.like(PoliceTagInfoXjdf::getOrganizationId, request.getOrganizationId().substring(0, 8));
            }


        }
        List<PoliceTagInfoXjdf> list = policeTagInfoXjdfService.list(queryWrapper);

        // 创建统计结果
        com.hl.archive.domain.dto.TagStatisticsDTO statistics = new com.hl.archive.domain.dto.TagStatisticsDTO();
        statistics.setTagType(request.getTagType());
        statistics.setTagTypeName(tagTypeEnum.getName());

        // 统计总人数和详细标签
        Set<String> uniquePersons = new HashSet<>();
        Map<String, Set<String>> tagPersonMap = new HashMap<>();

        for (PoliceTagInfoXjdf policeTagInfoXjdf : list) {
            uniquePersons.add(policeTagInfoXjdf.getIdCard());
            tagPersonMap.computeIfAbsent(policeTagInfoXjdf.getTagName(), k -> new HashSet<>()).add(policeTagInfoXjdf.getIdCard());
        }

        // 设置总人数
        statistics.setTotalCount(uniquePersons.size());

        // 设置详细统计
        List<com.hl.archive.domain.dto.TagStatisticsDTO.TagDetailStatistics> tagDetails = new ArrayList<>();
        for (Map.Entry<String, Set<String>> entry : tagPersonMap.entrySet()) {
            com.hl.archive.domain.dto.TagStatisticsDTO.TagDetailStatistics detail = new com.hl.archive.domain.dto.TagStatisticsDTO.TagDetailStatistics();
            detail.setTagName(entry.getKey());
            detail.setCount(entry.getValue().size());
            tagDetails.add(detail);
        }

        // 按人数降序排序
        tagDetails.sort((a, b) -> b.getCount().compareTo(a.getCount()));
        statistics.setTagDetails(tagDetails);

        return statistics;
    }

    /**
     * 获取登峰训练营和实战能力体系组合统计信息
     */
    public CombinedTagStatisticsDTO getCombinedTagStatistics(TagStatisticsQueryDTO request) {
        CombinedTagStatisticsDTO combinedStatistics = new CombinedTagStatisticsDTO();

        // 获取登峰训练营统计
        TagStatisticsQueryDTO dengfengRequest = new TagStatisticsQueryDTO();
        dengfengRequest.setOrganizationId(request.getOrganizationId());
        dengfengRequest.setTagType(TagTypeEnum.DENGFENG_TRAINING.getCode());
        com.hl.archive.domain.dto.TagStatisticsDTO dengfengStatistics = getTagStatistics(dengfengRequest);
        dengfengStatistics.setTagDetails(null);
        combinedStatistics.setDengfengTraining(dengfengStatistics);

        // 获取实战能力体系统计
        TagStatisticsQueryDTO combatRequest = new TagStatisticsQueryDTO();
        combatRequest.setOrganizationId(request.getOrganizationId());
        combatRequest.setTagType(TagTypeEnum.COMBAT_ABILITY.getCode());
        com.hl.archive.domain.dto.TagStatisticsDTO combatStatistics = getTagStatistics(combatRequest);
        combinedStatistics.setCombatAbility(combatStatistics);


        // 获取星火计划统计
        TagStatisticsQueryDTO xinghuoRequest = new TagStatisticsQueryDTO();
        xinghuoRequest.setOrganizationId(request.getOrganizationId());
        xinghuoRequest.setTagType(TagTypeEnum.XINGHUO_PLAN.getCode());
        com.hl.archive.domain.dto.TagStatisticsDTO xinghuoStatistics = getXinghuoStatistics(xinghuoRequest);
        combinedStatistics.setXinghuoPlan(xinghuoStatistics);

        // 添加警营先锋
        TagStatisticsQueryDTO jingyingRequest = new TagStatisticsQueryDTO();
        jingyingRequest.setOrganizationId(request.getOrganizationId());
        jingyingRequest.setTagType(TagTypeEnum.JINGYING_XIANFENG.getCode());
        com.hl.archive.domain.dto.TagStatisticsDTO jingyingStatistics = getTagStatistics(jingyingRequest);
        jingyingStatistics.setTagDetails(null);
        combinedStatistics.setJingyingXianfeng(jingyingStatistics);

        return combinedStatistics;
    }


    public com.hl.archive.domain.dto.TagStatisticsDTO getXinghuoStatistics(TagStatisticsQueryDTO request) {
        LambdaQueryWrapper<PoliceProjectEntryPerson> queryWrapper = Wrappers.<PoliceProjectEntryPerson>lambdaQuery();
        if (StrUtil.isNotBlank(request.getOrganizationId())) {
            queryWrapper.likeRight(StrUtil.isNotBlank(request.getOrganizationId()) && !ProjectCommonConstants.WU_JIN_ORG_CODE.equals(request.getOrganizationId()),
                    PoliceProjectEntryPerson::getOrganizationId, request.getOrganizationId().substring(0, 8));
        }

        long count = policeProjectEntryPersonService.count(queryWrapper);
        com.hl.archive.domain.dto.TagStatisticsDTO statistics = new com.hl.archive.domain.dto.TagStatisticsDTO();
        statistics.setTagType(TagTypeEnum.XINGHUO_PLAN.getCode());
        statistics.setTagTypeName(TagTypeEnum.XINGHUO_PLAN.getName());
        statistics.setTotalCount((int) count);
        return statistics;
    }

//    /**
//     * 标签穿透查询
//     */
//    public Page<PoliceBasicInfo> getPoliceListByTagType(TagDrillDownDTO request) {
//        // 验证标签类型
//        TagTypeEnum tagTypeEnum = TagTypeEnum.getByCode(request.getTagType());
//        if (tagTypeEnum == null) {
//            throw new IllegalArgumentException("无效的标签类型: " + request.getTagType());
//        }
//        // 收集所有相关的身份证号
//        Set<String> idCardSet;
//        if (request.getTagType().equals(TagTypeEnum.XINGHUO_PLAN.getCode())) {
//            LambdaQueryWrapper<PoliceProjectEntryPerson> queryWrapper = Wrappers.<PoliceProjectEntryPerson>lambdaQuery();
//            if (StrUtil.isNotBlank(request.getOrganizationId())) {
//                queryWrapper.likeRight(StrUtil.isNotBlank(request.getOrganizationId()) && !ProjectCommonConstants.WU_JIN_ORG_CODE.equals(request.getOrganizationId()),
//                        PoliceProjectEntryPerson::getOrganizationId, request.getOrganizationId().substring(0, 8));
//            }
//            List<PoliceProjectEntryPerson> list = policeProjectEntryPersonService.list(queryWrapper);
//
//            idCardSet = list.stream().map(PoliceProjectEntryPerson::getIdCard).collect(Collectors.toSet());
//
//        } else {
//            // 获取标签信息列表
//            LambdaQueryWrapper<PoliceTagInfoXjdf> queryWrapper = Wrappers.<PoliceTagInfoXjdf>lambdaQuery()
//                    .eq(PoliceTagInfoXjdf::getTagType, request.getTagType())
//                    .like(StrUtil.isNotBlank(request.getTagName()), PoliceTagInfoXjdf::getTagName, request.getTagName());
//            if (StrUtil.isNotBlank(request.getOrganizationId())){
//                queryWrapper.like(StrUtil.isNotBlank(request.getOrganizationId()) && !ProjectCommonConstants.WU_JIN_ORG_CODE.equals(request.getOrganizationId()),
//                        PoliceTagInfoXjdf::getOrganizationId, request.getOrganizationId().substring(0, 8));
//            }
//            List<PoliceTagInfoXjdf> list = policeTagInfoXjdfService.list(queryWrapper);
//            idCardSet = list.stream().map(PoliceTagInfoXjdf::getIdCard).collect(Collectors.toSet());
//        }
//
//
//        // 如果没有找到相关人员，返回空结果
//        if (idCardSet.isEmpty()) {
//            return new Page<>(request.getPage(), request.getLimit());
//        }
//
//        // 转换为List
//        List<String> idCardList = new ArrayList<>(idCardSet);
//
//        // 如果有组织ID，截取前8位
//        String organizationId = request.getOrganizationId();
//        if (StrUtil.isNotBlank(organizationId)) {
//            if (ProjectCommonConstants.WU_JIN_ORG_CODE.equals(organizationId)) {
//                organizationId = null;
//            } else {
//                organizationId = organizationId.substring(0, 8);
//            }
//        }
//
//
//        // 过滤身份证号列表（根据组织机构ID）
//        List<String> filteredIdCardList = filterIdCardsByOrganization(idCardList, organizationId);
//
//        // 分页处理
//        int total = filteredIdCardList.size();
//        int start = (request.getPage() - 1) * request.getLimit();
//        int end = Math.min(start + request.getLimit(), total);
//
//        List<String> pageIdCardList = new ArrayList<>();
//        if (start < total) {
//            pageIdCardList = filteredIdCardList.subList(start, end);
//        }
//
//        // 从UserCacheService获取用户信息并转换为PoliceBasicInfo
//        List<PoliceBasicInfo> resultList = new ArrayList<>();
//        for (String idCard : pageIdCardList) {
//            UserBasicInfoDTO userInfo = userCacheService.getUserByIdCard(idCard);
//            if (userInfo != null) {
//                PoliceBasicInfo policeBasicInfo = new PoliceBasicInfo();
//                // 复制基本信息
//                BeanUtils.copyProperties(userInfo, policeBasicInfo);
//                resultList.add(policeBasicInfo);
//            }
//        }
//
//        // 创建分页结果
//        Page<PoliceBasicInfo> resultPage = new Page<>(request.getPage(), request.getLimit(), total);
//        resultPage.setRecords(resultList);
//        return resultPage;
//    }

    /**
     * 标签穿透查询（优化版本，返回带标签详情的结果）
     */
    public Page<TagDrillDownReturnDTO> getPoliceListByTagTypeWithDetails(TagDrillDownDTO request) {
        // 验证标签类型
        TagTypeEnum tagTypeEnum = TagTypeEnum.getByCode(request.getTagType());
        if (tagTypeEnum == null) {
            throw new IllegalArgumentException("无效的标签类型: " + request.getTagType());
        }

        // 收集所有相关的身份证号和标签信息
        Map<String, List<TagDrillDownReturnDTO.TagDetail>> idCardTagMap = new HashMap<>();

        if (request.getTagType().equals(TagTypeEnum.XINGHUO_PLAN.getCode())) {
            // 处理星火计划
            LambdaQueryWrapper<PoliceProjectEntryPerson> queryWrapper = Wrappers.<PoliceProjectEntryPerson>lambdaQuery();
            if (StrUtil.isNotBlank(request.getOrganizationId())) {
                queryWrapper.likeRight(StrUtil.isNotBlank(request.getOrganizationId()) && !ProjectCommonConstants.WU_JIN_ORG_CODE.equals(request.getOrganizationId()),
                        PoliceProjectEntryPerson::getOrganizationId, request.getOrganizationId().substring(0, 8));
            }
            // 添加时间范围查询
            if (request.getStartDate() != null) {
                queryWrapper.ge(PoliceProjectEntryPerson::getEntryTime, request.getStartDate().atStartOfDay());
            }
            if (request.getEndDate() != null) {
                queryWrapper.le(PoliceProjectEntryPerson::getEntryTime, request.getEndDate().atTime(23, 59, 59));
            }
            List<PoliceProjectEntryPerson> list = policeProjectEntryPersonService.list(queryWrapper);

            for (PoliceProjectEntryPerson person : list) {
                TagDrillDownReturnDTO.TagDetail tagDetail = new TagDrillDownReturnDTO.TagDetail();
                tagDetail.setTagType(TagTypeEnum.XINGHUO_PLAN.getCode());
                tagDetail.setTagTypeName(TagTypeEnum.XINGHUO_PLAN.getName());
                tagDetail.setTagName("星火计划");

                if (person.getEntryTime() != null) {
                    tagDetail.setAwardDate(person.getEntryTime().toLocalDate());
                }
                idCardTagMap.computeIfAbsent(person.getIdCard(), k -> new ArrayList<>()).add(tagDetail);
            }
        } else if (request.getTagType().equals(TagTypeEnum.KEY_FOCUS.getCode())) {
            // 处理重点关注
            LambdaQueryWrapper<PoliceTagInfoZdgz> queryWrapper = Wrappers.<PoliceTagInfoZdgz>lambdaQuery()
                    .like(StrUtil.isNotBlank(request.getTagName()), PoliceTagInfoZdgz::getTagName, request.getTagName());
            if (StrUtil.isNotBlank(request.getOrganizationId())){
                queryWrapper.like(StrUtil.isNotBlank(request.getOrganizationId()) && !ProjectCommonConstants.WU_JIN_ORG_CODE.equals(request.getOrganizationId()),
                        PoliceTagInfoZdgz::getOrganizationId, request.getOrganizationId().substring(0, 8));
            }
            // 添加时间范围查询（基于创建时间）
            if (request.getStartDate() != null) {
                queryWrapper.ge(PoliceTagInfoZdgz::getCreatedAt, request.getStartDate().atStartOfDay());
            }
            if (request.getEndDate() != null) {
                queryWrapper.le(PoliceTagInfoZdgz::getCreatedAt, request.getEndDate().atTime(23, 59, 59));
            }
            if (StrUtil.isNotBlank(request.getUserType())){
                queryWrapper.eq(PoliceTagInfoZdgz::getUserType, request.getUserType());
            }
            List<PoliceTagInfoZdgz> list = policeTagInfoZdgzService.list(queryWrapper);

            for (PoliceTagInfoZdgz tagInfo : list) {
                TagDrillDownReturnDTO.TagDetail tagDetail = new TagDrillDownReturnDTO.TagDetail();
                tagDetail.setTagType(tagInfo.getTagType());
                tagDetail.setTagTypeName(tagTypeEnum.getName());
                tagDetail.setTagName(tagInfo.getTagName());
                tagDetail.setAwardDate(tagInfo.getCreatedAt() != null ? tagInfo.getCreatedAt().toLocalDate() : null);
                tagDetail.setRemark(tagInfo.getRemark());

                idCardTagMap.computeIfAbsent(tagInfo.getIdCard(), k -> new ArrayList<>()).add(tagDetail);
            }
        } else if (request.getTagType().equals(TagTypeEnum.HONORS_AWARDS.getCode())) {
            // 处理表彰奖励
            LambdaQueryWrapper<PoliceHonors> queryWrapper = Wrappers.<PoliceHonors>lambdaQuery()
                    .like(StrUtil.isNotBlank(request.getTagName()), PoliceHonors::getHonorName, request.getTagName());
            if (StrUtil.isNotBlank(request.getOrganizationId())){
                queryWrapper.like(StrUtil.isNotBlank(request.getOrganizationId()) && !ProjectCommonConstants.WU_JIN_ORG_CODE.equals(request.getOrganizationId()),
                        PoliceHonors::getOrganizationId, request.getOrganizationId().substring(0, 8));
            }
            // 添加时间范围查询（基于获奖时间）
            if (request.getStartDate() != null) {
                queryWrapper.ge(PoliceHonors::getAwardDate, request.getStartDate());
            }
            if (request.getEndDate() != null) {
                queryWrapper.le(PoliceHonors::getAwardDate, request.getEndDate());
            }
            List<PoliceHonors> list = policeHonorsService.list(queryWrapper);

            for (PoliceHonors honor : list) {
                TagDrillDownReturnDTO.TagDetail tagDetail = new TagDrillDownReturnDTO.TagDetail();
                tagDetail.setTagType(TagTypeEnum.HONORS_AWARDS.getCode());
                tagDetail.setTagTypeName(TagTypeEnum.HONORS_AWARDS.getName());
                tagDetail.setTagName(honor.getHonorName());
                tagDetail.setAwardDate(honor.getAwardDate());
                tagDetail.setRemark(honor.getApproveAuthority());
                idCardTagMap.computeIfAbsent(honor.getIdCard(), k -> new ArrayList<>()).add(tagDetail);
            }
        } else {
            // 处理其他标签类型（登峰训练营、实战能力体系等）
            LambdaQueryWrapper<PoliceTagInfoXjdf> queryWrapper = Wrappers.<PoliceTagInfoXjdf>lambdaQuery()
                    .eq(PoliceTagInfoXjdf::getTagType, request.getTagType())
                    .like(StrUtil.isNotBlank(request.getTagName()), PoliceTagInfoXjdf::getTagName, request.getTagName());
            if (StrUtil.isNotBlank(request.getOrganizationId())){
                queryWrapper.like(StrUtil.isNotBlank(request.getOrganizationId()) && !ProjectCommonConstants.WU_JIN_ORG_CODE.equals(request.getOrganizationId()),
                        PoliceTagInfoXjdf::getOrganizationId, request.getOrganizationId().substring(0, 8));
            }
            // 添加时间范围查询（基于获得时间）
            if (request.getStartDate() != null) {
                queryWrapper.ge(PoliceTagInfoXjdf::getAwardDate, request.getStartDate());
            }
            if (request.getEndDate() != null) {
                queryWrapper.le(PoliceTagInfoXjdf::getAwardDate, request.getEndDate());
            }

            if (request.getUserType() != null) {
                queryWrapper.eq(PoliceTagInfoXjdf::getUserType, request.getUserType());
            }

            List<PoliceTagInfoXjdf> list = policeTagInfoXjdfService.list(queryWrapper);

            for (PoliceTagInfoXjdf tagInfo : list) {
                TagDrillDownReturnDTO.TagDetail tagDetail = new TagDrillDownReturnDTO.TagDetail();
                tagDetail.setTagType(tagInfo.getTagType());
                tagDetail.setTagTypeName(tagTypeEnum.getName());
                tagDetail.setTagName(tagInfo.getTagName());
                tagDetail.setAwardDate(tagInfo.getAwardDate());
                tagDetail.setRemark(tagInfo.getRemark());

                idCardTagMap.computeIfAbsent(tagInfo.getIdCard(), k -> new ArrayList<>()).add(tagDetail);
            }
        }

        // 如果没有找到相关人员，返回空结果
        if (idCardTagMap.isEmpty()) {
            return new Page<>(request.getPage(), request.getLimit());
        }

        // 转换为List
        List<String> idCardList = new ArrayList<>(idCardTagMap.keySet());

        // 如果有组织ID，截取前8位
        String organizationId = request.getOrganizationId();
        if (StrUtil.isNotBlank(organizationId)) {
            if (ProjectCommonConstants.WU_JIN_ORG_CODE.equals(organizationId)) {
                organizationId = null;
            } else {
                organizationId = organizationId.substring(0, 8);
            }
        }

        // 过滤身份证号列表（根据组织机构ID）
        List<String> filteredIdCardList = filterIdCardsByOrganization(idCardList, organizationId);

        // 分页处理
        int total = filteredIdCardList.size();
        int start = (request.getPage() - 1) * request.getLimit();
        int end = Math.min(start + request.getLimit(), total);

        List<String> pageIdCardList = new ArrayList<>();
        if (start < total) {
            pageIdCardList = filteredIdCardList.subList(start, end);
        }

        // 从UserCacheService获取用户信息并转换为TagDrillDownReturnDTO
        List<TagDrillDownReturnDTO> resultList = new ArrayList<>();
        for (String idCard : pageIdCardList) {
            UserBasicInfoDTO userInfo = userCacheService.getUserByIdCard(idCard);
            if (userInfo != null) {
                TagDrillDownReturnDTO dto = new TagDrillDownReturnDTO();
                // 复制基本信息
                BeanUtils.copyProperties(userInfo, dto);
                // 设置标签详情
                dto.setTagDetails(idCardTagMap.get(idCard));
                resultList.add(dto);
            }
        }

        // 创建分页结果
        Page<TagDrillDownReturnDTO> resultPage = new Page<>(request.getPage(), request.getLimit(), total);

        resultPage.setRecords(resultList);
        return resultPage;
    }


    public List<TagCountResultDTO> selectTagCount(PoliceBaseQueryDTO request) {

        List<PoliceTagInfoXjdf> list = policeTagInfoXjdfService.list(Wrappers.<PoliceTagInfoXjdf>lambdaQuery()
                .eq(PoliceTagInfoXjdf::getIdCard, request.getIdCard())
                .in(PoliceTagInfoXjdf::getTagType, ListUtil.of(TagTypeEnum.DENGFENG_TRAINING.getCode(), TagTypeEnum.COMBAT_ABILITY.getCode())));


        Map<String, List<PoliceTagInfoXjdf>> tagType = list.stream()
                .collect(Collectors.groupingBy(PoliceTagInfoXjdf::getTagType));

        List<TagCountResultDTO> result = new ArrayList<>();

        for (Map.Entry<String, List<PoliceTagInfoXjdf>> entry : tagType.entrySet()) {
            String key = entry.getKey();
            List<PoliceTagInfoXjdf> value = entry.getValue();
            List<TagCountResultDTO.TagDetail> tagNameList = new ArrayList<>();
            for (PoliceTagInfoXjdf tagInfo : value) {
                String tagName = tagInfo.getTagName();
                LocalDate awardDate = tagInfo.getAwardDate();

                TagCountResultDTO.TagDetail tagDetail = new TagCountResultDTO.TagDetail();
                tagDetail.setTagName(tagName);
                tagDetail.setAwardDate(awardDate);
                tagNameList.add(tagDetail);

            }
            TagCountResultDTO resultDTO = new TagCountResultDTO();
            resultDTO.setTagType(key);
            resultDTO.setTagNameList(tagNameList);
            resultDTO.setTagTypeName(TagTypeEnum.getByCode(key).getName());
            result.add(resultDTO);
        }
        return result;
    }

    public Page<PolicePersonalTagReturnDTO> pagePolicePersonalTag(PolicePersonalTagQueryDTO param) {
        if (StrUtil.isNotBlank(param.getOrganizationId())) {
            if (!ProjectCommonConstants.WU_JIN_ORG_CODE.equals(param.getOrganizationId())) {
                param.setOrganizationId(param.getOrganizationId().substring(0, 8));
            } else {
                param.setOrganizationId(ProjectCommonConstants.WU_JIN_ORG_CODE);
            }
        } else {
            param.setOrganizationId(null);
        }

        Page<PolicePersonalTagReturnDTO> page = policeTagInfoMapper.pagePolicePersonalTag(Page.of(param.getPage(), param.getLimit()), param);
        return page;
    }

    /**
     * 根据组织机构ID过滤身份证号列表
     * @param idCardList 身份证号列表
     * @param organizationId 组织机构ID
     * @return 过滤后的身份证号列表
     */
    private List<String> filterIdCardsByOrganization(List<String> idCardList, String organizationId) {
        if (StrUtil.isBlank(organizationId)) {
            return idCardList;
        }

        List<String> filteredList = new ArrayList<>();
        for (String idCard : idCardList) {
            UserBasicInfoDTO userInfo = userCacheService.getUserByIdCard(idCard);
            if (userInfo != null && userInfo.getOrganizationId() != null) {
                // 检查组织机构ID是否匹配（前8位匹配）
                if (userInfo.getOrganizationId().startsWith(organizationId)) {
                    filteredList.add(idCard);
                }
            }
        }
        return filteredList;
    }
}

