package com.hl.archive.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.idev.excel.FastExcel;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.constant.ProjectCommonConstants;
import com.hl.archive.domain.dto.PolicePersonWarnRecordRequestDTO;
import com.hl.archive.domain.dto.PolicePersonWarnRecordTxTaskCreateDTO;
import com.hl.archive.domain.dto.PolicePersonWarnRecordTxTaskQueryDTO;
import com.hl.archive.domain.dto.WarnTypeStatisticsDTO;
import com.hl.archive.domain.dto.WarnTypeStatisticsRequestDTO;
import com.hl.archive.domain.entity.PolicePersonWarnRecord;
import com.hl.archive.domain.entity.PolicePersonWarnRecordTask;
import com.hl.archive.enums.PersonWarnTypeEnum;
import com.hl.archive.feign.TaskApi;
import com.hl.archive.mapper.PolicePersonWarnRecordMapper;
import com.hl.archive.utils.SsoCacheUtil;
import com.hl.common.config.exception.HlErrException;
import com.hl.common.domain.R;
import com.hl.security.UserUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PolicePersonWarnRecordService extends ServiceImpl<PolicePersonWarnRecordMapper, PolicePersonWarnRecord> {

    private final TaskApi taskApi;

    private final PolicePersonWarnRecordTaskService policePersonWarnRecordTaskService;


    public Page<PolicePersonWarnRecord> pageList(PolicePersonWarnRecordRequestDTO requestDTO) {

        if (StrUtil.isNotBlank(requestDTO.getOrganizationId())) {
            if (!ProjectCommonConstants.WU_JIN_ORG_CODE.equals(requestDTO.getOrganizationId())) {
                // 非320412000000时，截取前8位
                requestDTO.setOrganizationId(requestDTO.getOrganizationId().substring(0, 8));
            } else {
                requestDTO.setOrganizationId(null);
            }
        }
        Page<PolicePersonWarnRecord> page = this.page(Page.of(requestDTO.getPage(), requestDTO.getLimit()), Wrappers.<PolicePersonWarnRecord>lambdaQuery()
                .like(StrUtil.isNotBlank(requestDTO.getIdCard()), PolicePersonWarnRecord::getIdCard, requestDTO.getIdCard())
                .eq(StrUtil.isNotBlank(requestDTO.getWarnType()), PolicePersonWarnRecord::getWarnType, requestDTO.getWarnType())
                .like(StrUtil.isNotBlank(requestDTO.getName()), PolicePersonWarnRecord::getName, requestDTO.getName())
                .likeRight(StrUtil.isNotBlank(requestDTO.getOrganizationId()), PolicePersonWarnRecord::getOrganizationId, requestDTO.getOrganizationId())
                .ge(requestDTO.getWarnStartTime() != null, PolicePersonWarnRecord::getWarnTime, requestDTO.getWarnStartTime())
                .le(requestDTO.getWarnEndTime() != null, PolicePersonWarnRecord::getWarnTime, requestDTO.getWarnEndTime())
                .eq(requestDTO.getSignStatus() != null, PolicePersonWarnRecord::getSignStatus, requestDTO.getSignStatus())
                .and(StrUtil.isNotBlank(requestDTO.getQuery()), w ->
                        w.like(StrUtil.isNotBlank(requestDTO.getQuery()), PolicePersonWarnRecord::getName, requestDTO.getQuery())
                                .or()
                                .like(StrUtil.isNotBlank(requestDTO.getQuery()), PolicePersonWarnRecord::getIdCard, requestDTO.getQuery())
                                .or()
                                .like(StrUtil.isNotBlank(requestDTO.getQuery()), PolicePersonWarnRecord::getPoliceNumber, requestDTO.getQuery())
                                .or()
                                .like(StrUtil.isNotBlank(requestDTO.getQuery()), PolicePersonWarnRecord::getDescription, requestDTO.getQuery())
                                .or()
                                .like(StrUtil.isNotBlank(requestDTO.getQuery()), PolicePersonWarnRecord::getDataKey, requestDTO.getQuery())
                )
                .orderByDesc(PolicePersonWarnRecord::getWarnTime));
        return page;
    }

    public void exportWarnInfo(PolicePersonWarnRecordRequestDTO requestDTO, HttpServletResponse response) throws IOException {
        requestDTO.setLimit(Integer.MAX_VALUE);
        Page<PolicePersonWarnRecord> policePersonWarnRecordPage = this.pageList(requestDTO);
        List<PolicePersonWarnRecord> records = policePersonWarnRecordPage.getRecords();
        for (PolicePersonWarnRecord record : records) {
            String warnType = record.getWarnType();
            record.setWarnType(Objects.requireNonNull(PersonWarnTypeEnum.getByCode(warnType)).getLabel());
            record.setOrganizationId(SsoCacheUtil.getOrganizationName(record.getOrganizationId()));
            String dataType = record.getDataType();
            switch (dataType) {
                case "jq":
                    record.setDataType("警情");
                    break;
                case "xf":
                    record.setDataType("信访");
                    break;
                case "myjc":
                    record.setDataType("民警监测");
                    break;
                case "aj":
                    record.setDataType("案件");
                    break;
                default:
                    record.setDataType("");
                    break;
            }
        }
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        FastExcel.write(response.getOutputStream(), PolicePersonWarnRecord.class).autoCloseStream(Boolean.FALSE).sheet("数据导出")
                .doWrite(records);
    }

    public JSONArray createTxTask(PolicePersonWarnRecordTxTaskCreateDTO requestDTO) {
        JSONObject param = new JSONObject();
        param.put("config_uuid", "CIHS71KS27P");
        param.put("btxjfr", requestDTO.getBtxjfr());
        param.put("txthr", requestDTO.getTxthr());
        param.put("txthyy", requestDTO.getTxthyy());
        String token = UserUtils.getUser().getToken();
        R<JSONArray> taskRes = taskApi.add(token, param);
        if (taskRes.getErrno() == 200) {
            String taskId = taskRes.getData().getString(0);
            PolicePersonWarnRecordTask recordTask = new PolicePersonWarnRecordTask();
            recordTask.setWarnId(Long.valueOf(requestDTO.getDataId()));
            // 谈心谈话类型数据
            recordTask.setTaskType("txth");
            recordTask.setTaskId(taskId);
            boolean save = policePersonWarnRecordTaskService.save(recordTask);
            if (!save) {
                throw new HlErrException("任务创建失败");
            }
            return taskRes.getData();
        } else {
            throw new HlErrException("任务创建失败");
        }
    }

    public R<?> listTaskInfo(PolicePersonWarnRecordTxTaskQueryDTO requestDTO) {
        List<PolicePersonWarnRecordTask> list = policePersonWarnRecordTaskService.list(Wrappers.<PolicePersonWarnRecordTask>lambdaQuery()
                .eq(PolicePersonWarnRecordTask::getWarnId, requestDTO.getDataId())
                .eq(StrUtil.isNotBlank(requestDTO.getTaskType()), PolicePersonWarnRecordTask::getTaskType, requestDTO.getTaskType()));
        List<String> taskIdList = list.stream().map(PolicePersonWarnRecordTask::getTaskId).collect(Collectors.toList());
        if (taskIdList.isEmpty()) {
            return R.ok();
        }
        JSONObject param = new JSONObject();
        // 后期多个任务类型
        param.put("config_uuid", "CIHS71KS27P");
        param.put("task_ids", taskIdList);
        String token = UserUtils.getUser().getToken();
        R<JSONObject> taskList = taskApi.getTaskList(token, param);
        return taskList;
    }


    public void importTrafficData() {
        ExcelReader reader = ExcelUtil.getReader("C:\\Users\\<USER>\\Desktop\\民警违法\\exp_2025-09-19.xls");
        List<Map<String, Object>> mapList = reader.readAll();
        Map<String, String> codeMap = new HashMap<>();
        for (Map<String, Object> map : mapList) {
            String code = map.get("本地代码").toString();
            String actionName = map.get("行为简称").toString();
            codeMap.put(code, actionName);
        }
        reader.close();
        ExcelReader dataReader = ExcelUtil.getReader("C:\\Users\\<USER>\\Desktop\\民警违法\\结果集1 (10).xls");
        List<Map<String, Object>> mapList1 = dataReader.readAll();
        for (Map<String, Object> map : mapList1) {
            String action = map.get("T1.违法行为").toString();
            String idCard = map.get("T2.身份证号码").toString();
            String time = map.get("T1.违法时间").toString();

            PolicePersonWarnRecord warnRecord = new PolicePersonWarnRecord();
            warnRecord.setWarnType(PersonWarnTypeEnum.JIAO_TONG_WEI_ZHANG.getCode());
            warnRecord.setIdCard(idCard);
            Object objByIdCard = SsoCacheUtil.getUserObjByIdCard(idCard);
            if (objByIdCard == null) {
                continue;
            }
            JSONObject from = JSONObject.from(objByIdCard);

            warnRecord.setPoliceNumber(from.getString("police_id"));
            warnRecord.setWarnTime(DateUtil.parse(time).toLocalDateTime());
            warnRecord.setOrganizationId(from.getByPath("organization[0].organization_id").toString());
            warnRecord.setName(from.getString("name"));
            warnRecord.setDescription(action + " " + codeMap.get(action));

            LambdaQueryWrapper<PolicePersonWarnRecord> policePersonWarnRecordLambdaQueryWrapper = Wrappers.lambdaQuery(warnRecord);
            PolicePersonWarnRecord one = this.getOne(policePersonWarnRecordLambdaQueryWrapper);
            if (one != null) {
                log.error("数据已经存在");
            } else {
                save(warnRecord);
            }

        }
        dataReader.close();
    }

    /**
     * 统计不同预警类别的数量
     * @param requestDTO 统计请求参数
     * @return 预警类别统计结果
     */
    public List<WarnTypeStatisticsDTO> getWarnTypeStatistics(WarnTypeStatisticsRequestDTO requestDTO) {
        String organizationId = requestDTO.getOrganizationId();

        // 处理组织机构ID逻辑，与分页查询保持一致
        if (StrUtil.isNotBlank(organizationId)) {
            if (!ProjectCommonConstants.WU_JIN_ORG_CODE.equals(organizationId)) {
                // 非320412000000时，截取前8位
                organizationId = organizationId.substring(0, 8);
            } else {
                organizationId = null;
            }
        }

        List<WarnTypeStatisticsDTO> statistics = baseMapper.getWarnTypeStatistics(
                organizationId,
                requestDTO.getStartTime(),
                requestDTO.getEndTime()
        );

        // 为每个统计结果设置预警类型名称
        for (WarnTypeStatisticsDTO stat : statistics) {
            PersonWarnTypeEnum warnTypeEnum = PersonWarnTypeEnum.getByCode(stat.getWarnTypeCode());
            if (warnTypeEnum != null) {
                stat.setWarnTypeName(warnTypeEnum.getLabel());
            } else {
                stat.setWarnTypeName("未知类型");
            }
        }

        return statistics;
    }
}
