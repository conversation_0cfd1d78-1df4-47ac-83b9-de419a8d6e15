package com.hl.archive.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 新警排名类型枚举
 */
@Getter
@AllArgsConstructor
public enum NewPoliceRankingTypeEnum {

    /**
     * 接处警排名
     */
    POLICE_CALL("POLICE_CALL", "接处警排名"),

    /**
     * 办案排名
     */
    CASE_HANDLING("CASE_HANDLING", "办案排名");

    /**
     * 排名类型代码
     */
    private final String code;

    /**
     * 排名类型名称
     */
    private final String name;

    /**
     * 根据代码获取枚举
     * @param code 代码
     * @return 枚举值
     */
    public static NewPoliceRankingTypeEnum getByCode(String code) {
        for (NewPoliceRankingTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
