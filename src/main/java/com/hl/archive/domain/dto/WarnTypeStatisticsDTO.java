package com.hl.archive.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 预警类别统计DTO
 */
@Data
@ApiModel(description = "预警类别统计DTO")
public class WarnTypeStatisticsDTO {

    /**
     * 预警类型代码
     */
    @ApiModelProperty(value = "预警类型代码")
    private String warnTypeCode;

    /**
     * 预警类型名称
     */
    @ApiModelProperty(value = "预警类型名称")
    private String warnTypeName;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Long count;


}
