package com.hl.archive.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * 标签穿透查询请求DTO
 */
@Data
@ApiModel(description = "标签穿透查询请求DTO")
public class TagDrillDownDTO {

    /**
     * 组织ID（可选）
     */
    @ApiModelProperty(value = "组织ID")
    private String organizationId;

    /**
     * 标签类型
     * dengfeng_training: 登峰训练营
     * combat_ability: 实战能力体系
     * key_focus: 重点关注
     * honors_awards: 表彰奖励
     * xing_huo: 星火计划
     * jingying_xianfeng: 警营先锋
     */
    @ApiModelProperty(value = "标签类型", required = true)
    private String tagType;

    /**
     * 标签名称（可选，用于查询特定标签的人员）
     */
    @ApiModelProperty(value = "标签名称")
    private String tagName;

    /**
     * 开始时间（可选，用于查询指定时间范围内获得标签的人员）
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    /**
     * 结束时间（可选，用于查询指定时间范围内获得标签的人员）
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    /**
     * 页码（从1开始）
     */
    @ApiModelProperty(value = "页码", example = "1")
    private Integer page = 1;

    /**
     * 每页大小
     */
    @ApiModelProperty(value = "每页大小", example = "20")
    private Integer limit = 20;

    private String userType;
}
