package com.hl.archive.domain.dto;

import com.alibaba.fastjson2.JSONObject;
import com.hl.archive.utils.TransConstants;
import com.hl.translation.annotation.Translation;
import lombok.Data;

import java.util.List;

@Data
public class PolicePersonalTagReturnDTO {


    private String idCard;

    private List<String> tagNameList;

    private List<String> tagTypeList;

    @Translation(type = TransConstants.ID_CARD_TO_USER_OBJ, mapper = "idCard")
    private JSONObject personInfo;
}
