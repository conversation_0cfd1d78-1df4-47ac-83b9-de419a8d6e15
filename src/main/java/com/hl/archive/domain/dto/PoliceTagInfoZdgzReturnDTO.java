package com.hl.archive.domain.dto;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hl.archive.utils.TransConstants;
import com.hl.translation.annotation.Translation;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

@Data
public class PoliceTagInfoZdgzReturnDTO {

    private String idCard;

    private List<String> tagNameList;

    private List<Integer> ids;

    private String remark;

    @Translation(type = TransConstants.ID_CARD_TO_USER_OBJ, mapper = "idCard")
    private JSONObject personInfo;

    private String createdBy;

    @Translation(type = TransConstants.ID_CARD_TO_USER_OBJ, mapper = "createdBy")
    private JSONObject createdUserInfo;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate createdAt;
}
