package com.hl.archive.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;

@Data
public class PolicePersonalTagQueryDTO {

    private String tagType;

    private String idCard;

    private String organizationId;

    private Integer page;

    private Integer limit;


    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;
}
