package com.hl.archive.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * 用户基本信息DTO - 汇聚民警和辅警的共有基本信息
 */
@ApiModel(description = "用户基本信息DTO")
@Data
public class UserBasicInfoDTO {

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    /**
     * 警号（仅民警有）
     */
    @ApiModelProperty(value = "警号")
    private String policeNumber;

    /**
     * 工号（仅辅警有）
     */
    @ApiModelProperty(value = "工号")
    private String employeeNumber;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String gender;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthDate;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String unitName;

    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    private String department;

    /**
     * 岗位/职务
     */
    @ApiModelProperty(value = "岗位/职务")
    private String position;

    /**
     * 学历
     */
    @ApiModelProperty(value = "学历")
    private String educationLevel;

    /**
     * 政治面貌
     */
    @ApiModelProperty(value = "政治面貌")
    private String politicalStatus;

    /**
     * 婚姻状况
     */
    @ApiModelProperty(value = "婚姻状况")
    private String maritalStatus;

    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码")
    private String phoneNumber;

    /**
     * 血型
     */
    @ApiModelProperty(value = "血型")
    private String bloodType;

    /**
     * 民族
     */
    @ApiModelProperty(value = "民族")
    private String nation;

    /**
     * 籍贯
     */
    @ApiModelProperty(value = "籍贯")
    private String nativePlace;

    /**
     * 用户类型：POLICE-民警，AUXILIARY-辅警
     */
    @ApiModelProperty(value = "用户类型")
    private String userType;

    /**
     * 组织ID
     */
    @ApiModelProperty(value = "组织ID")
    private String organizationId;

    /**
     * 在岗状态
     */
    @ApiModelProperty(value = "在岗状态")
    private String dutyStatus;

    /**
     * 照片地址
     */
    @ApiModelProperty(value = "照片地址")
    private String imgUrl;

    private String organizationName;


    /**
     * 用户类型枚举
     */
    public enum UserType {
        POLICE("POLICE", "民警"),
        AUXILIARY("AUXILIARY", "辅警");

        private final String code;
        private final String name;

        UserType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }
}
