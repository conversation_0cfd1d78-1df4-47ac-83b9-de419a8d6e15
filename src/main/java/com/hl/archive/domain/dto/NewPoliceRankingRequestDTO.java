package com.hl.archive.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 新警排名统计请求DTO
 */
@Data
@ApiModel(description = "新警排名统计请求DTO")
public class NewPoliceRankingRequestDTO {

    /**
     * 组织机构ID
     */
    @ApiModelProperty(value = "组织机构ID")
    private String organizationId;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 排名类型：POLICE_CALL-接处警排名，CASE_HANDLING-办案排名
     */
    @ApiModelProperty(value = "排名类型：POLICE_CALL-接处警排名，CASE_HANDLING-办案排名")
    private String rankingType;

    /**
     * 分页页码
     */
    @ApiModelProperty(value = "分页页码")
    private Integer page = 1;

    /**
     * 分页大小
     */
    @ApiModelProperty(value = "分页大小")
    private Integer limit = 20;
}
