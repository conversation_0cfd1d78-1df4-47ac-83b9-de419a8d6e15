package com.hl.archive.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * 用户搜索查询DTO
 */
@ApiModel(description = "用户搜索查询DTO")
@Data
public class UserSearchQueryDTO {

    @ApiModelProperty(value = "页码", example = "1")
    private Integer page = 1;

    @ApiModelProperty(value = "每页大小", example = "20")
    private Integer limit = 20;

    // ========== 精确查询条件 ==========
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    @ApiModelProperty(value = "警号")
    private String policeNumber;

    @ApiModelProperty(value = "工号")
    private String employeeNumber;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @ApiModelProperty(value = "部门")
    private String department;

    @ApiModelProperty(value = "岗位/职务")
    private String position;

    @ApiModelProperty(value = "学历")
    private String educationLevel;

    @ApiModelProperty(value = "政治面貌")
    private String politicalStatus;

    @ApiModelProperty(value = "婚姻状况")
    private String maritalStatus;

    @ApiModelProperty(value = "血型")
    private String bloodType;

    @ApiModelProperty(value = "民族")
    private String nation;

    @ApiModelProperty(value = "籍贯")
    private String nativePlace;

    @ApiModelProperty(value = "用户类型：POLICE-民警，AUXILIARY-辅警")
    private String userType;

    @ApiModelProperty(value = "组织ID")
    private String organizationId;

    @ApiModelProperty(value = "在岗状态")
    private String dutyStatus;

    // ========== 范围查询条件 ==========
    @ApiModelProperty(value = "出生日期开始")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthDateStart;

    @ApiModelProperty(value = "出生日期结束")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthDateEnd;

    @ApiModelProperty(value = "年龄范围开始")
    private Integer ageStart;

    @ApiModelProperty(value = "年龄范围结束")
    private Integer ageEnd;

    // ========== 模糊查询条件 ==========
    @ApiModelProperty(value = "关键字搜索（姓名、身份证、警号、工号、单位、部门、职务等）")
    private String keyword;

    @ApiModelProperty(value = "手机号码（支持模糊查询）")
    private String phoneNumber;

    // ========== 排序条件 ==========
    @ApiModelProperty(value = "排序字段")
    private String orderBy;

    @ApiModelProperty(value = "排序方向：ASC-升序，DESC-降序")
    private String orderDirection = "ASC";
}
