package com.hl.archive.domain.dto;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hl.archive.utils.TransConstants;
import com.hl.translation.annotation.Translation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * 新警排名统计结果DTO
 */
@Data
@ApiModel(description = "新警排名统计结果DTO")
public class NewPoliceRankingDTO {

    /**
     * 排名
     */
    @ApiModelProperty(value = "排名")
    private Integer ranking;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    private String idCard;


    /**
     * 数量（接处警数量或办案数量）
     */
    @ApiModelProperty(value = "数量（接处警数量或办案数量）")
    private Long count;

    /**
     * 排名类型
     */
    @ApiModelProperty(value = "排名类型：POLICE_CALL-接处警排名，CASE_HANDLING-办案排名")
    private String rankingType;

    /**
     * 用户信息（从身份证号翻译得到）
     */
    @Translation(type = TransConstants.USER_INFO_ID_CARD, mapper = "idCard")
    @ApiModelProperty(value = "用户信息")
    private JSONObject userInfo;


}
