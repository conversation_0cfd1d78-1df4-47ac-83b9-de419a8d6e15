package com.hl.archive.domain.dto;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hl.archive.utils.TransConstants;
import com.hl.translation.annotation.Translation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * 新警排名统计结果DTO
 */
@Data
@ApiModel(description = "新警排名统计结果DTO")
public class NewPoliceRankingDTO {

    /**
     * 排名
     */
    @ApiModelProperty(value = "排名")
    private Integer ranking;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String gender;

    /**
     * 单位ID
     */
    @ApiModelProperty(value = "单位ID")
    private String organizationId;

    /**
     * 单位名称
     */
    @Translation(type = TransConstants.ORGANIZATION_TO_NAME, mapper = "organizationId")
    @ApiModelProperty(value = "单位名称")
    private String organizationName;

    /**
     * 到岗时间
     */
    @ApiModelProperty(value = "到岗时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate onboardDate;

    /**
     * 数量（接处警数量或办案数量）
     */
    @ApiModelProperty(value = "数量（接处警数量或办案数量）")
    private Long count;

    /**
     * 排名类型
     */
    @ApiModelProperty(value = "排名类型：POLICE_CALL-接处警排名，CASE_HANDLING-办案排名")
    private String rankingType;

    /**
     * 用户信息（从身份证号翻译得到）
     */
    @Translation(type = TransConstants.ID_CARD_TO_USER_OBJ, mapper = "idCard")
    @ApiModelProperty(value = "用户信息")
    private JSONObject userInfo;

    public NewPoliceRankingDTO() {
    }

    public NewPoliceRankingDTO(String idCard, String name, String gender, String organizationId, 
                              LocalDate onboardDate, Long count, String rankingType) {
        this.idCard = idCard;
        this.name = name;
        this.gender = gender;
        this.organizationId = organizationId;
        this.onboardDate = onboardDate;
        this.count = count;
        this.rankingType = rankingType;
    }
}
