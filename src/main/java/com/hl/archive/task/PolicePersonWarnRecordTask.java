package com.hl.archive.task;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.DbUtil;
import cn.hutool.db.Entity;
import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hl.archive.domain.entity.PoliceBasicInfo;
import com.hl.archive.domain.entity.PolicePersonWarnRecord;
import com.hl.archive.enums.PersonWarnTypeEnum;
import com.hl.archive.service.PoliceBasicInfoService;
import com.hl.archive.service.PolicePersonWarnRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 民警预警定时任务
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PolicePersonWarnRecordTask {

    private final PolicePersonWarnRecordService policePersonWarnRecordService;

    private final PoliceBasicInfoService policeBasicInfoService;

    @Resource(name = "datasource1DataSource")
    private DataSource jqDataSource;

    @Resource(name = "datasource3DataSource")
    private DataSource xfDataSource;

    /**
     * 涉警
     */
    @JobExecutor(name = "refreshPersonWarnRecord")
    public void refreshPersonWarnRecord() {
        try {
            List<PoliceBasicInfo> list = policeBasicInfoService.list();
            for (PoliceBasicInfo basicInfo : list) {
                String idCard = basicInfo.getIdCard();
                List<Entity> query = DbUtil.use(jqDataSource).query("select jjbh from wjsc_jq_sjxx where zxbs = 0 and   gmsfhm = '" + idCard + "'");
                if (query.isEmpty()) {
                    continue;
                }
                String jjbh = query.stream().map(s -> "'" + s.getStr("jjbh") + "'").collect(Collectors.joining(","));
                List<Entity> jqData = DbUtil.use(jqDataSource).query("select * from view_police_export where  jjbh in (" + jjbh + ")");
                for (Entity jqDatum : jqData) {
                    PolicePersonWarnRecord one = policePersonWarnRecordService.getOne(Wrappers.<PolicePersonWarnRecord>lambdaQuery()
                            .eq(PolicePersonWarnRecord::getIdCard, idCard)
                            .eq(PolicePersonWarnRecord::getWarnType, PersonWarnTypeEnum.SHE_JING.getCode())
                            .eq(PolicePersonWarnRecord::getDataKey, jqDatum.getStr("jjbh"))
                            .last(" limit 1"));
                    if (one != null) {
                        continue;
                    }
                    PolicePersonWarnRecord warnRecord = new PolicePersonWarnRecord();
                    warnRecord.setIdCard(idCard);
                    warnRecord.setName(basicInfo.getName());
                    warnRecord.setPoliceNumber(basicInfo.getPoliceNumber());
                    warnRecord.setOrganizationId(basicInfo.getOrganizationId());
                    warnRecord.setWarnTime(DateUtil.parse(jqDatum.getStr("bjdhsj_time")).toLocalDateTime());
                    warnRecord.setWarnType(PersonWarnTypeEnum.SHE_JING.getCode());
                    warnRecord.setDataType("jq");
                    warnRecord.setDataKey(jqDatum.getStr("jjbh"));
                    warnRecord.setDescription(jqDatum.getStr("cljgnr"));
                    policePersonWarnRecordService.save(warnRecord);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }


    /**
     * 信访
     */
    @JobExecutor(name = "refreshXfWarning")
    public void refreshXfWarning() {
        try {
            List<PoliceBasicInfo> list = policeBasicInfoService.list();
            for (PoliceBasicInfo policeBasicInfo : list) {
                String idCard = policeBasicInfo.getIdCard();
                String policeNumber = policeBasicInfo.getPoliceNumber();
                List<Entity> query = DbUtil.use(xfDataSource).query("select * from view_petition_task_info where major_police like '%" + policeNumber + "%'");
                if (query.isEmpty()) {
                    continue;
                }
                for (Entity entity : query) {
                    PolicePersonWarnRecord one = policePersonWarnRecordService.getOne(Wrappers.<PolicePersonWarnRecord>lambdaQuery()
                            .eq(PolicePersonWarnRecord::getIdCard, idCard)
                            .eq(PolicePersonWarnRecord::getWarnType, PersonWarnTypeEnum.SHE_JU_BAO_TOU_SU.getCode())
                            .eq(PolicePersonWarnRecord::getDataKey, entity.getStr("task_id"))
                            .last(" limit 1"));
                    if (one != null) {
                        continue;
                    }
                    PolicePersonWarnRecord warnRecord = new PolicePersonWarnRecord();
                    warnRecord.setIdCard(idCard);
                    warnRecord.setName(policeBasicInfo.getName());
                    warnRecord.setPoliceNumber(policeNumber);
                    warnRecord.setOrganizationId(policeBasicInfo.getOrganizationId());
                    if (StrUtil.isNotBlank(entity.getStr("accept_time"))){
                        warnRecord.setWarnTime(DateUtil.parse(entity.getStr("accept_time")).toLocalDateTime());
                    }
                    warnRecord.setWarnType(PersonWarnTypeEnum.SHE_JU_BAO_TOU_SU.getCode());
                    warnRecord.setDataType("xf");
                    warnRecord.setDataKey(entity.getStr("task_id"));
                    warnRecord.setDescription(entity.getStr("relative_theme"));
                    policePersonWarnRecordService.save(warnRecord);
                }

            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }


    /**
     * 民意监测
     */
    @JobExecutor(name = "refreshMyjcWarning")
    public void refreshMyjcWarning() {
        try {
            List<PoliceBasicInfo> list = policeBasicInfoService.list();
            for (PoliceBasicInfo policeBasicInfo : list) {
                String idCard = policeBasicInfo.getIdCard();
                String policeNumber = policeBasicInfo.getPoliceNumber();
                List<Entity> query = DbUtil.use(xfDataSource).query("select * from view_opinion_task where involved_police like '%" + policeNumber + "%'");
                if (query.isEmpty()) {
                    continue;
                }
                for (Entity entity : query) {
                    PolicePersonWarnRecord one = policePersonWarnRecordService.getOne(Wrappers.<PolicePersonWarnRecord>lambdaQuery()
                            .eq(PolicePersonWarnRecord::getIdCard, idCard)
                            .eq(PolicePersonWarnRecord::getWarnType, PersonWarnTypeEnum.SHE_JU_BAO_TOU_SU.getCode())
                            .eq(PolicePersonWarnRecord::getDataKey, entity.getStr("task_id"))
                            .last(" limit 1"));
                    if (one != null) {
                        continue;
                    }
                    PolicePersonWarnRecord warnRecord = new PolicePersonWarnRecord();
                    warnRecord.setIdCard(idCard);
                    warnRecord.setName(policeBasicInfo.getName());
                    warnRecord.setPoliceNumber(policeNumber);
                    warnRecord.setOrganizationId(policeBasicInfo.getOrganizationId());
                    if (StrUtil.isNotBlank(entity.getStr("occur_time"))){
                        warnRecord.setWarnTime(DateUtil.parse(entity.getStr("occur_time")).toLocalDateTime());
                    }
                    warnRecord.setWarnType(PersonWarnTypeEnum.SHE_JU_BAO_TOU_SU.getCode());
                    warnRecord.setDataType("myjc");
                    warnRecord.setDataKey(entity.getStr("task_id"));
                    warnRecord.setDescription(entity.getStr("claimant_content"));
                    policePersonWarnRecordService.save(warnRecord);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }


    /**
     * 民警涉案
     */
    @JobExecutor(name = "refreshPoliceCaseWarnRecord")
    public void refreshPoliceCaseWarnRecord() {
        try {
            List<PoliceBasicInfo> list = policeBasicInfoService.list();
            for (PoliceBasicInfo basicInfo : list) {
                String idCard = basicInfo.getIdCard();
               List<Entity> crimePersonList = DbUtil.use(xfDataSource).query("select * from case_crime_person where person_card = '" + idCard + "'");
               if (crimePersonList.isEmpty()){
                   continue;
               }
                for (Entity caseData : crimePersonList) {
                    PolicePersonWarnRecord one = policePersonWarnRecordService.getOne(Wrappers.<PolicePersonWarnRecord>lambdaQuery()
                            .eq(PolicePersonWarnRecord::getIdCard, idCard)
                            .eq(PolicePersonWarnRecord::getWarnType, PersonWarnTypeEnum.SHE_AN.getCode())
                            .eq(PolicePersonWarnRecord::getDataKey, caseData.getStr("involved_case_no"))
                            .last(" limit 1"));
                    if (one != null) {
                        continue;
                    }
                    PolicePersonWarnRecord warnRecord = new PolicePersonWarnRecord();
                    warnRecord.setIdCard(idCard);
                    warnRecord.setName(basicInfo.getName());
                    warnRecord.setPoliceNumber(basicInfo.getPoliceNumber());
                    warnRecord.setOrganizationId(basicInfo.getOrganizationId());
                    warnRecord.setWarnTime(DateUtil.parse(caseData.getStr("scrk_time")).toLocalDateTime());
                    warnRecord.setWarnType(PersonWarnTypeEnum.SHE_AN.getCode());
                    warnRecord.setDataType("aj");
                    warnRecord.setDataKey(caseData.getStr("involved_case_no"));
                    warnRecord.setDescription(caseData.getStr("details"));
                    policePersonWarnRecordService.save(warnRecord);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }
}
