<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceNewProfileMapper">

    <!-- 新警接处警数量排名查询 -->
    <select id="getNewPoliceCallRanking" resultType="com.hl.archive.domain.dto.NewPoliceRankingDTO">
        SELECT
            pnp.id_card as idCard,
            pnp.name as name,
            pnp.gender as gender,
            pnp.organization_id as organizationId,
            pnp.onboard_date as onboardDate,
            COALESCE(call_count.count, 0) as count,
            'POLICE_CALL' as rankingType
        FROM police_new_profile pnp
        LEFT JOIN (
            -- TODO: 请在这里添加实际的接处警数量查询逻辑
            -- 示例：从接处警表中统计每个新警的接处警数量
            -- SELECT id_card, COUNT(*) as count
            -- FROM police_call_record
            -- WHERE 1=1
            -- <if test="startTime != null">
            --     AND call_time >= #{startTime}
            -- </if>
            -- <if test="endTime != null">
            --     AND call_time &lt;= #{endTime}
            -- </if>
            -- GROUP BY id_card
            SELECT pnp2.id_card, 0 as count FROM police_new_profile pnp2 WHERE 1=2
        ) call_count ON pnp.id_card = call_count.id_card
        WHERE pnp.is_deleted = 0
        <if test="organizationId != null and organizationId != ''">
            AND pnp.organization_id LIKE CONCAT(#{organizationId}, '%')
        </if>
        ORDER BY count DESC, pnp.name ASC
    </select>

    <!-- 新警办案数量排名查询 -->
    <select id="getNewPoliceCaseRanking" resultType="com.hl.archive.domain.dto.NewPoliceRankingDTO">
        SELECT
            pnp.id_card as idCard,
            pnp.name as name,
            pnp.gender as gender,
            pnp.organization_id as organizationId,
            pnp.onboard_date as onboardDate,
            COALESCE(case_count.count, 0) as count,
            'CASE_HANDLING' as rankingType
        FROM police_new_profile pnp
        LEFT JOIN (
            -- TODO: 请在这里添加实际的办案数量查询逻辑
            -- 示例：从办案表中统计每个新警的办案数量
            -- SELECT id_card, COUNT(*) as count
            -- FROM police_case_record
            -- WHERE 1=1
            -- <if test="startTime != null">
            --     AND case_time >= #{startTime}
            -- </if>
            -- <if test="endTime != null">
            --     AND case_time &lt;= #{endTime}
            -- </if>
            -- GROUP BY id_card
            SELECT pnp2.id_card, 0 as count FROM police_new_profile pnp2 WHERE 1=2
        ) case_count ON pnp.id_card = case_count.id_card
        WHERE pnp.is_deleted = 0
        <if test="organizationId != null and organizationId != ''">
            AND pnp.organization_id LIKE CONCAT(#{organizationId}, '%')
        </if>
        ORDER BY count DESC, pnp.name ASC
    </select>

</mapper>