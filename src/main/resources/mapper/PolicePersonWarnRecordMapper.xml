<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PolicePersonWarnRecordMapper">

    <!-- 统计不同预警类别的数量 -->
    <select id="getWarnTypeStatistics" resultType="com.hl.archive.domain.dto.WarnTypeStatisticsDTO">
        SELECT
            warn_type as warnTypeCode,
            COUNT(*) as count
        FROM police_person_warn_record
        WHERE is_deleted = 0
        <if test="organizationId != null and organizationId != ''">
            AND organization_id LIKE CONCAT(#{organizationId}, '%')
        </if>
        <if test="startTime != null">
            AND warn_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND warn_time &lt;= #{endTime}
        </if>
        GROUP BY warn_type
        ORDER BY count DESC
    </select>

</mapper>