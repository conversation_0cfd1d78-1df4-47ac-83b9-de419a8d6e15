# 用户搜索接口使用示例

## 1. 快速搜索示例

### 搜索姓名包含"张"的用户
```bash
curl -X GET "http://localhost:8080/user-cache/quick-search?keyword=张&page=1&limit=10"
```

### 搜索身份证号
```bash
curl -X GET "http://localhost:8080/user-cache/quick-search?keyword=320412199001011234"
```

### 搜索警号
```bash
curl -X GET "http://localhost:8080/user-cache/quick-search?keyword=001234"
```

### 搜索单位
```bash
curl -X GET "http://localhost:8080/user-cache/quick-search?keyword=派出所&page=1&limit=20"
```

## 2. 高级搜索示例

### 搜索男性民警
```bash
curl -X POST "http://localhost:8080/user-cache/search" \
  -H "Content-Type: application/json" \
  -d '{
    "gender": "男",
    "userType": "POLICE",
    "page": 1,
    "limit": 20
  }'
```

### 搜索25-35岁的用户
```bash
curl -X POST "http://localhost:8080/user-cache/search" \
  -H "Content-Type: application/json" \
  -d '{
    "ageStart": 25,
    "ageEnd": 35,
    "page": 1,
    "limit": 10,
    "orderBy": "name",
    "orderDirection": "ASC"
  }'
```

### 搜索特定单位的本科学历人员
```bash
curl -X POST "http://localhost:8080/user-cache/search" \
  -H "Content-Type: application/json" \
  -d '{
    "unitName": "某某派出所",
    "educationLevel": "本科",
    "page": 1,
    "limit": 50
  }'
```

### 复合条件搜索
```bash
curl -X POST "http://localhost:8080/user-cache/search" \
  -H "Content-Type: application/json" \
  -d '{
    "gender": "女",
    "userType": "POLICE",
    "educationLevel": "本科",
    "politicalStatus": "中共党员",
    "dutyStatus": "在岗",
    "keyword": "治安",
    "page": 1,
    "limit": 20,
    "orderBy": "name",
    "orderDirection": "ASC"
  }'
```

## 3. 条件搜索示例

### 搜索男性用户
```bash
curl -X GET "http://localhost:8080/user-cache/search-by-conditions?gender=男&page=1&limit=20"
```

### 搜索特定组织的用户
```bash
curl -X GET "http://localhost:8080/user-cache/search-by-conditions?organizationId=320412000000&page=1&limit=50"
```

### 搜索辅警
```bash
curl -X GET "http://localhost:8080/user-cache/search-by-conditions?userType=AUXILIARY&page=1&limit=30"
```

### 多条件组合搜索
```bash
curl -X GET "http://localhost:8080/user-cache/search-by-conditions?gender=男&userType=POLICE&educationLevel=本科&page=1&limit=20&orderBy=name&orderDirection=ASC"
```

## 4. JavaScript前端调用示例

### 快速搜索
```javascript
async function quickSearch(keyword, page = 1, limit = 20) {
  try {
    const response = await fetch(`/user-cache/quick-search?keyword=${encodeURIComponent(keyword)}&page=${page}&limit=${limit}`);
    const result = await response.json();
    
    if (result.code === 200) {
      console.log('搜索成功:', result.data);
      return result.data;
    } else {
      console.error('搜索失败:', result.message);
      return null;
    }
  } catch (error) {
    console.error('请求失败:', error);
    return null;
  }
}

// 使用示例
quickSearch('张三').then(data => {
  if (data) {
    console.log(`找到 ${data.total} 条记录`);
    data.users.forEach(user => {
      console.log(`${user.name} - ${user.unitName} - ${user.userType}`);
    });
  }
});
```

### 高级搜索
```javascript
async function advancedSearch(searchParams) {
  try {
    const response = await fetch('/user-cache/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(searchParams)
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      return result.data;
    } else {
      console.error('搜索失败:', result.message);
      return null;
    }
  } catch (error) {
    console.error('请求失败:', error);
    return null;
  }
}

// 使用示例：搜索25-35岁的男性民警
const searchParams = {
  gender: '男',
  userType: 'POLICE',
  ageStart: 25,
  ageEnd: 35,
  page: 1,
  limit: 20,
  orderBy: 'name',
  orderDirection: 'ASC'
};

advancedSearch(searchParams).then(data => {
  if (data) {
    console.log('搜索结果:', data);
    console.log('统计信息:', data.statistics);
  }
});
```

### 条件搜索
```javascript
async function searchByConditions(conditions) {
  const params = new URLSearchParams();
  
  Object.keys(conditions).forEach(key => {
    if (conditions[key] !== null && conditions[key] !== undefined && conditions[key] !== '') {
      params.append(key, conditions[key]);
    }
  });
  
  try {
    const response = await fetch(`/user-cache/search-by-conditions?${params.toString()}`);
    const result = await response.json();
    
    if (result.code === 200) {
      return result.data;
    } else {
      console.error('搜索失败:', result.message);
      return null;
    }
  } catch (error) {
    console.error('请求失败:', error);
    return null;
  }
}

// 使用示例
const conditions = {
  gender: '女',
  userType: 'POLICE',
  unitName: '派出所',
  page: 1,
  limit: 20
};

searchByConditions(conditions).then(data => {
  if (data) {
    console.log(`找到 ${data.total} 条记录`);
  }
});
```

## 5. 响应数据结构说明

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "total": 100,           // 总记录数
    "page": 1,              // 当前页码
    "limit": 20,            // 每页大小
    "totalPages": 5,        // 总页数
    "users": [              // 用户列表
      {
        "idCard": "身份证号",
        "policeNumber": "警号",
        "employeeNumber": "工号",
        "name": "姓名",
        "gender": "性别",
        "birthDate": "出生日期",
        "unitName": "单位名称",
        "department": "部门",
        "position": "职务",
        "educationLevel": "学历",
        "politicalStatus": "政治面貌",
        "maritalStatus": "婚姻状况",
        "phoneNumber": "手机号",
        "bloodType": "血型",
        "nation": "民族",
        "nativePlace": "籍贯",
        "userType": "用户类型",
        "organizationId": "组织ID",
        "dutyStatus": "在岗状态",
        "imgUrl": "照片地址"
      }
    ],
    "statistics": {         // 统计信息
      "policeCount": 80,    // 民警数量
      "auxiliaryCount": 20, // 辅警数量
      "maleCount": 60,      // 男性数量
      "femaleCount": 40,    // 女性数量
      "onDutyCount": 95,    // 在岗数量
      "offDutyCount": 5     // 离岗数量
    }
  }
}
```

### 错误响应
```json
{
  "code": 500,
  "message": "搜索用户信息失败: 具体错误信息",
  "data": null
}
```

## 6. 性能优化建议

1. **合理设置分页大小**: 建议每页20-50条记录
2. **使用关键字搜索**: 对于模糊查询，优先使用keyword参数
3. **避免过大范围查询**: 年龄范围不要设置过大
4. **缓存预热**: 确保缓存中有足够的数据
5. **监控缓存状态**: 定期检查缓存统计信息
